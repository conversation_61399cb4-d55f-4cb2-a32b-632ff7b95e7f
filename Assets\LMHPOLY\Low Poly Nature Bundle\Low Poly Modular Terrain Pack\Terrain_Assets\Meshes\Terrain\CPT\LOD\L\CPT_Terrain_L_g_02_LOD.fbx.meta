fileFormatVersion: 2
guid: 200ab97cc5cba164282b8fb6e9e182c7
ModelImporter:
  serializedVersion: 19301
  internalIDToNameTable:
  - first:
      1: 100000
    second: //RootNode
  - first:
      1: 100002
    second: CPT_Terrain_L_g_02_LOD0
  - first:
      1: 100004
    second: CPT_Terrain_L_g_02_LOD1
  - first:
      1: 100006
    second: CPT_Terrain_L_g_02_LOD2
  - first:
      4: 400000
    second: //RootNode
  - first:
      4: 400002
    second: CPT_Terrain_L_g_02_LOD0
  - first:
      4: 400004
    second: CPT_Terrain_L_g_02_LOD1
  - first:
      4: 400006
    second: CPT_Terrain_L_g_02_LOD2
  - first:
      23: 2300000
    second: CPT_Terrain_L_g_02_LOD0
  - first:
      23: 2300002
    second: CPT_Terrain_L_g_02_LOD1
  - first:
      23: 2300004
    second: CPT_Terrain_L_g_02_LOD2
  - first:
      33: 3300000
    second: CPT_Terrain_L_g_02_LOD0
  - first:
      33: 3300002
    second: CPT_Terrain_L_g_02_LOD1
  - first:
      33: 3300004
    second: CPT_Terrain_L_g_02_LOD2
  - first:
      43: 4300000
    second: CPT_Terrain_L_g_02_LOD2
  - first:
      43: 4300002
    second: CPT_Terrain_L_g_02_LOD1
  - first:
      43: 4300004
    second: CPT_Terrain_L_g_02_LOD0
  - first:
      64: 6400000
    second: CPT_Terrain_L_g_02_LOD0
  - first:
      64: 6400002
    second: CPT_Terrain_L_g_02_LOD1
  - first:
      64: 6400004
    second: CPT_Terrain_L_g_02_LOD2
  - first:
      205: 20500000
    second: //RootNode
  externalObjects: {}
  materials:
    materialImportMode: 0
    materialName: 0
    materialSearch: 1
    materialLocation: 0
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages:
    - 0.25
    - 0.125
    - 0.01
    globalScale: 1
    meshCompression: 0
    addColliders: 1
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 1
    importCameras: 0
    importLights: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    meshOptimizationFlags: -1
    indexFormat: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 0
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 1
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 0
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 0
  humanoidOversampling: 1
  avatarSetup: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
