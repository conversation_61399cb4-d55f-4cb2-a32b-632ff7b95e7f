{"_version": "4.6.1", "_rootDirectory": "Assets/PluginMaster/DesignTools/Editor/PrefabWorldBuilder", "_autoSavePeriodMinutes": 1, "_undoPalette": true, "_controlPointSize": 2, "_selectedContolPointColor": {"r": 0.0, "g": 1.0, "b": 1.0, "a": 1.0}, "_selectedControlPointBlink": false, "_closeAllWindowsWhenClosingTheToolbar": false, "_openToolPropertiesWhenAToolIsSelected": false, "_selectTheNextPaletteInAlphabeticalOrder": true, "_thumbnailLayer": 7, "_openBrushPropertiesWhenABrushIsSelected": false, "_maxPreviewCountInEditMode": 200, "_createThumbnailsFolder": false, "_renameItemParent": false, "_showInfoText": true, "_unsavedChangesAction": 0, "_tempCollidersAction": 1, "_paletteManager": {"_selectedPaletteIdx": 0, "_selectedBrushIdx": 1, "_showBrushName": false, "_viewList": false, "_showTabsInMultipleRows": false, "_iconSize": 64}, "_floorManager": {"_profileKeys": ["<PERSON><PERSON><PERSON>"], "_profileValues": [{"_moduleSizeType": 1, "_moduleSize": {"x": 1.0, "y": 1.0, "z": 1.0}, "_spacing": {"x": 0.0, "y": 0.0, "z": 0.0}, "_upwardAxis": {"_axis": 1, "_sign": 1}, "_forwardAxis": {"_axis": 2, "_sign": 1}, "_paintTool": {"_parentGlobalId": "", "_autoCreateParent": true, "_setSurfaceAsParent": false, "_createSubparentPerPalette": true, "_createSubparentPerTool": true, "_createSubparentPerBrush": false, "_createSubparentPerPrefab": false, "_overwritePrefabLayer": false, "_layer": 0, "_overwriteBrushProperties": false, "_brushSettings": {"_id": -1, "_surfaceDistance": 0.0, "_randomSurfaceDistance": false, "_randomSurfaceDistanceRange": {"_v1": -0.004999999888241291, "_v2": 0.004999999888241291, "_min": -0.004999999888241291, "_max": 0.004999999888241291}, "_embedInSurface": false, "_embedAtPivotHeight": true, "_localPositionOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_rotateToTheSurface": true, "_eulerOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_addRandomRotation": false, "_rotationFactor": 90.0, "_rotateInMultiples": false, "_randomEulerOffset": {"x": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "y": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "z": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}}, "_alwaysOrientUp": false, "_separateScaleAxes": false, "_scaleMultiplier": {"x": 1.0, "y": 1.0, "z": 1.0}, "_randomScaleMultiplier": false, "_randomScaleMultiplierRange": {"x": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "y": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "z": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}}, "_flipX": 0, "_flipY": 0, "_thumbnailSettings": {"_backgroudColor": {"r": 0.5, "g": 0.5, "b": 0.5, "a": 1.0}, "_lightEuler": {"x": 130.0, "y": -165.0}, "_lightColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}, "_lightIntensity": 1.0, "_zoom": 1.0, "_targetEuler": {"x": 0.0, "y": 125.0, "z": 0.0}, "_targetOffset": {"x": 0.0, "y": 0.0}, "_useCustomImage": false}}}, "_swapXZ": false, "_sizes": [{"_name": "<PERSON><PERSON><PERSON>", "_size": {"x": 1.0, "y": 1.0, "z": 1.0}}], "_selectedSizeName": "<PERSON><PERSON><PERSON>"}], "_selectedProfileName": "<PERSON><PERSON><PERSON>", "_unsavedProfile": {"_moduleSizeType": 1, "_moduleSize": {"x": 2.0, "y": 1.6499998569488525, "z": 2.0}, "_spacing": {"x": 0.0, "y": 0.0, "z": 0.0}, "_upwardAxis": {"_axis": 1, "_sign": 1}, "_forwardAxis": {"_axis": 2, "_sign": 1}, "_paintTool": {"_parentGlobalId": "", "_autoCreateParent": true, "_setSurfaceAsParent": false, "_createSubparentPerPalette": true, "_createSubparentPerTool": true, "_createSubparentPerBrush": false, "_createSubparentPerPrefab": false, "_overwritePrefabLayer": false, "_layer": 0, "_overwriteBrushProperties": false, "_brushSettings": {"_id": -1, "_surfaceDistance": 0.0, "_randomSurfaceDistance": false, "_randomSurfaceDistanceRange": {"_v1": -0.004999999888241291, "_v2": 0.004999999888241291, "_min": -0.004999999888241291, "_max": 0.004999999888241291}, "_embedInSurface": false, "_embedAtPivotHeight": true, "_localPositionOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_rotateToTheSurface": true, "_eulerOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_addRandomRotation": false, "_rotationFactor": 90.0, "_rotateInMultiples": false, "_randomEulerOffset": {"x": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "y": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "z": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}}, "_alwaysOrientUp": false, "_separateScaleAxes": false, "_scaleMultiplier": {"x": 1.0, "y": 1.0, "z": 1.0}, "_randomScaleMultiplier": false, "_randomScaleMultiplierRange": {"x": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "y": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "z": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}}, "_flipX": 0, "_flipY": 0, "_thumbnailSettings": {"_backgroudColor": {"r": 0.5, "g": 0.5, "b": 0.5, "a": 1.0}, "_lightEuler": {"x": 130.0, "y": -165.0}, "_lightColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}, "_lightIntensity": 1.0, "_zoom": 1.0, "_targetEuler": {"x": 0.0, "y": 125.0, "z": 0.0}, "_targetOffset": {"x": 0.0, "y": 0.0}, "_useCustomImage": false}}}, "_swapXZ": false, "_sizes": [{"_name": "<PERSON><PERSON><PERSON>", "_size": {"x": 1.0, "y": 1.0, "z": 1.0}}], "_selectedSizeName": "<PERSON><PERSON><PERSON>"}}, "_wallManager": {"_profileKeys": ["<PERSON><PERSON><PERSON>"], "_profileValues": [{"_moduleSizeType": 1, "_moduleSize": {"x": 1.0, "y": 1.0, "z": 1.0}, "_spacing": {"x": 0.0, "y": 0.0, "z": 0.0}, "_upwardAxis": {"_axis": 1, "_sign": 1}, "_forwardAxis": {"_axis": 2, "_sign": 1}, "_paintTool": {"_parentGlobalId": "", "_autoCreateParent": true, "_setSurfaceAsParent": false, "_createSubparentPerPalette": true, "_createSubparentPerTool": true, "_createSubparentPerBrush": false, "_createSubparentPerPrefab": false, "_overwritePrefabLayer": false, "_layer": 0, "_overwriteBrushProperties": false, "_brushSettings": {"_id": -1, "_surfaceDistance": 0.0, "_randomSurfaceDistance": false, "_randomSurfaceDistanceRange": {"_v1": -0.004999999888241291, "_v2": 0.004999999888241291, "_min": -0.004999999888241291, "_max": 0.004999999888241291}, "_embedInSurface": false, "_embedAtPivotHeight": true, "_localPositionOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_rotateToTheSurface": true, "_eulerOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_addRandomRotation": false, "_rotationFactor": 90.0, "_rotateInMultiples": false, "_randomEulerOffset": {"x": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "y": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "z": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}}, "_alwaysOrientUp": false, "_separateScaleAxes": false, "_scaleMultiplier": {"x": 1.0, "y": 1.0, "z": 1.0}, "_randomScaleMultiplier": false, "_randomScaleMultiplierRange": {"x": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "y": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "z": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}}, "_flipX": 0, "_flipY": 0, "_thumbnailSettings": {"_backgroudColor": {"r": 0.5, "g": 0.5, "b": 0.5, "a": 1.0}, "_lightEuler": {"x": 130.0, "y": -165.0}, "_lightColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}, "_lightIntensity": 1.0, "_zoom": 1.0, "_targetEuler": {"x": 0.0, "y": 125.0, "z": 0.0}, "_targetOffset": {"x": 0.0, "y": 0.0}, "_useCustomImage": false}}}, "_autoCalculateAxes": true, "_sizes": [{"_name": "<PERSON><PERSON><PERSON>", "_size": 1.0}], "_selectedSizeName": "<PERSON><PERSON><PERSON>"}], "_selectedProfileName": "<PERSON><PERSON><PERSON>", "_unsavedProfile": {"_moduleSizeType": 1, "_moduleSize": {"x": 2.0, "y": 1.6499998569488525, "z": 2.0}, "_spacing": {"x": 0.0, "y": 0.0, "z": 0.0}, "_upwardAxis": {"_axis": 1, "_sign": 1}, "_forwardAxis": {"_axis": 2, "_sign": 1}, "_paintTool": {"_parentGlobalId": "", "_autoCreateParent": true, "_setSurfaceAsParent": false, "_createSubparentPerPalette": true, "_createSubparentPerTool": true, "_createSubparentPerBrush": false, "_createSubparentPerPrefab": false, "_overwritePrefabLayer": false, "_layer": 0, "_overwriteBrushProperties": false, "_brushSettings": {"_id": -1, "_surfaceDistance": 0.0, "_randomSurfaceDistance": false, "_randomSurfaceDistanceRange": {"_v1": -0.004999999888241291, "_v2": 0.004999999888241291, "_min": -0.004999999888241291, "_max": 0.004999999888241291}, "_embedInSurface": false, "_embedAtPivotHeight": true, "_localPositionOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_rotateToTheSurface": true, "_eulerOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_addRandomRotation": false, "_rotationFactor": 90.0, "_rotateInMultiples": false, "_randomEulerOffset": {"x": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "y": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "z": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}}, "_alwaysOrientUp": false, "_separateScaleAxes": false, "_scaleMultiplier": {"x": 1.0, "y": 1.0, "z": 1.0}, "_randomScaleMultiplier": false, "_randomScaleMultiplierRange": {"x": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "y": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "z": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}}, "_flipX": 0, "_flipY": 0, "_thumbnailSettings": {"_backgroudColor": {"r": 0.5, "g": 0.5, "b": 0.5, "a": 1.0}, "_lightEuler": {"x": 130.0, "y": -165.0}, "_lightColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}, "_lightIntensity": 1.0, "_zoom": 1.0, "_targetEuler": {"x": 0.0, "y": 125.0, "z": 0.0}, "_targetOffset": {"x": 0.0, "y": 0.0}, "_useCustomImage": false}}}, "_autoCalculateAxes": true, "_sizes": [{"_name": "<PERSON><PERSON><PERSON>", "_size": 1.0}], "_selectedSizeName": "<PERSON><PERSON><PERSON>"}}, "_pinManager": {"_profileKeys": ["<PERSON><PERSON><PERSON>"], "_profileValues": [{"_paintOnMeshesWithoutCollider": false, "_paintOnSelectedOnly": false, "_paintOnPalettePrefabs": false, "_mode": 0, "_paralellToTheSurface": true, "_repeat": false, "_flatteningSettings": {"_hardness": 0.0, "_padding": 0.0, "_clearTrees": true, "_clearDetails": true}, "_flattenTerrain": false, "_avoidOverlapping": false, "_snapRotationToGrid": false, "_paintTool": {"_parentGlobalId": "", "_autoCreateParent": true, "_setSurfaceAsParent": false, "_createSubparentPerPalette": true, "_createSubparentPerTool": true, "_createSubparentPerBrush": false, "_createSubparentPerPrefab": false, "_overwritePrefabLayer": false, "_layer": 0, "_overwriteBrushProperties": false, "_brushSettings": {"_id": -1, "_surfaceDistance": 0.0, "_randomSurfaceDistance": false, "_randomSurfaceDistanceRange": {"_v1": -0.004999999888241291, "_v2": 0.004999999888241291, "_min": -0.004999999888241291, "_max": 0.004999999888241291}, "_embedInSurface": false, "_embedAtPivotHeight": true, "_localPositionOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_rotateToTheSurface": true, "_eulerOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_addRandomRotation": false, "_rotationFactor": 90.0, "_rotateInMultiples": false, "_randomEulerOffset": {"x": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "y": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "z": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}}, "_alwaysOrientUp": false, "_separateScaleAxes": false, "_scaleMultiplier": {"x": 1.0, "y": 1.0, "z": 1.0}, "_randomScaleMultiplier": false, "_randomScaleMultiplierRange": {"x": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "y": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "z": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}}, "_flipX": 0, "_flipY": 0, "_thumbnailSettings": {"_backgroudColor": {"r": 0.5, "g": 0.5, "b": 0.5, "a": 1.0}, "_lightEuler": {"x": 130.0, "y": -165.0}, "_lightColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}, "_lightIntensity": 1.0, "_zoom": 1.0, "_targetEuler": {"x": 0.0, "y": 125.0, "z": 0.0}, "_targetOffset": {"x": 0.0, "y": 0.0}, "_useCustomImage": false}}}}], "_selectedProfileName": "<PERSON><PERSON><PERSON>", "_unsavedProfile": {"_paintOnMeshesWithoutCollider": false, "_paintOnSelectedOnly": false, "_paintOnPalettePrefabs": false, "_mode": 0, "_paralellToTheSurface": true, "_repeat": false, "_flatteningSettings": {"_hardness": 0.0, "_padding": 0.0, "_clearTrees": true, "_clearDetails": true}, "_flattenTerrain": false, "_avoidOverlapping": false, "_snapRotationToGrid": false, "_paintTool": {"_parentGlobalId": "", "_autoCreateParent": true, "_setSurfaceAsParent": false, "_createSubparentPerPalette": true, "_createSubparentPerTool": true, "_createSubparentPerBrush": false, "_createSubparentPerPrefab": false, "_overwritePrefabLayer": false, "_layer": 0, "_overwriteBrushProperties": false, "_brushSettings": {"_id": -1, "_surfaceDistance": 0.0, "_randomSurfaceDistance": false, "_randomSurfaceDistanceRange": {"_v1": -0.004999999888241291, "_v2": 0.004999999888241291, "_min": -0.004999999888241291, "_max": 0.004999999888241291}, "_embedInSurface": false, "_embedAtPivotHeight": true, "_localPositionOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_rotateToTheSurface": true, "_eulerOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_addRandomRotation": false, "_rotationFactor": 90.0, "_rotateInMultiples": false, "_randomEulerOffset": {"x": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "y": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "z": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}}, "_alwaysOrientUp": false, "_separateScaleAxes": false, "_scaleMultiplier": {"x": 1.0, "y": 1.0, "z": 1.0}, "_randomScaleMultiplier": false, "_randomScaleMultiplierRange": {"x": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "y": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "z": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}}, "_flipX": 0, "_flipY": 0, "_thumbnailSettings": {"_backgroudColor": {"r": 0.5, "g": 0.5, "b": 0.5, "a": 1.0}, "_lightEuler": {"x": 130.0, "y": -165.0}, "_lightColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}, "_lightIntensity": 1.0, "_zoom": 1.0, "_targetEuler": {"x": 0.0, "y": 125.0, "z": 0.0}, "_targetOffset": {"x": 0.0, "y": 0.0}, "_useCustomImage": false}}}}, "_rotationSnapValue": 5.0}, "_brushManager": {"_profileKeys": ["<PERSON><PERSON><PERSON>"], "_profileValues": [{"_radius": 1.0, "_paintTool": {"_parentGlobalId": "", "_autoCreateParent": true, "_setSurfaceAsParent": false, "_createSubparentPerPalette": true, "_createSubparentPerTool": true, "_createSubparentPerBrush": false, "_createSubparentPerPrefab": false, "_overwritePrefabLayer": false, "_layer": 0, "_overwriteBrushProperties": false, "_brushSettings": {"_id": -1, "_surfaceDistance": 0.0, "_randomSurfaceDistance": false, "_randomSurfaceDistanceRange": {"_v1": -0.004999999888241291, "_v2": 0.004999999888241291, "_min": -0.004999999888241291, "_max": 0.004999999888241291}, "_embedInSurface": false, "_embedAtPivotHeight": true, "_localPositionOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_rotateToTheSurface": true, "_eulerOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_addRandomRotation": false, "_rotationFactor": 90.0, "_rotateInMultiples": false, "_randomEulerOffset": {"x": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "y": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "z": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}}, "_alwaysOrientUp": false, "_separateScaleAxes": false, "_scaleMultiplier": {"x": 1.0, "y": 1.0, "z": 1.0}, "_randomScaleMultiplier": false, "_randomScaleMultiplierRange": {"x": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "y": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "z": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}}, "_flipX": 0, "_flipY": 0, "_thumbnailSettings": {"_backgroudColor": {"r": 0.5, "g": 0.5, "b": 0.5, "a": 1.0}, "_lightEuler": {"x": 130.0, "y": -165.0}, "_lightColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}, "_lightIntensity": 1.0, "_zoom": 1.0, "_targetEuler": {"x": 0.0, "y": 125.0, "z": 0.0}, "_targetOffset": {"x": 0.0, "y": 0.0}, "_useCustomImage": false}}}, "_brushShape": 1, "_density": 50, "_orientAlongBrushstroke": false, "_additionalOrientationAngle": {"x": 0.0, "y": 0.0, "z": 0.0}, "_spacingType": 0, "_minSpacing": 1.0, "_randomizePositions": true, "_randomness": 1.0, "_paintOnSurfaceToolSettings": {"_paintOnMeshesWithoutCollider": false, "_paintOnSelectedOnly": false, "_paintOnPalettePrefabs": false, "_mode": 0, "_paralellToTheSurface": true}, "_maxHeightFromCenter": 2.0, "_heightType": 1, "_avoidOverlapping": 4, "_layerFilter": {"serializedVersion": "2", "m_Bits": 4294967295}, "_tagFilter": ["Untagged", "Respawn", "Finish", "Editor<PERSON><PERSON><PERSON>", "MainCamera", "Player", "GameController", "AI", "ignoreSensor", "Ground", "carBody", "CheckPoint"], "_slopeFilter": {"_v1": 0.0, "_v2": 60.0, "_min": 0.0, "_max": 60.0}, "_terrainLayerIds": [], "_showPreview": false}], "_selectedProfileName": "<PERSON><PERSON><PERSON>", "_unsavedProfile": {"_radius": 1.0, "_paintTool": {"_parentGlobalId": "", "_autoCreateParent": true, "_setSurfaceAsParent": false, "_createSubparentPerPalette": true, "_createSubparentPerTool": true, "_createSubparentPerBrush": false, "_createSubparentPerPrefab": false, "_overwritePrefabLayer": false, "_layer": 0, "_overwriteBrushProperties": false, "_brushSettings": {"_id": -1, "_surfaceDistance": 0.0, "_randomSurfaceDistance": false, "_randomSurfaceDistanceRange": {"_v1": -0.004999999888241291, "_v2": 0.004999999888241291, "_min": -0.004999999888241291, "_max": 0.004999999888241291}, "_embedInSurface": false, "_embedAtPivotHeight": true, "_localPositionOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_rotateToTheSurface": true, "_eulerOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_addRandomRotation": false, "_rotationFactor": 90.0, "_rotateInMultiples": false, "_randomEulerOffset": {"x": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "y": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "z": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}}, "_alwaysOrientUp": false, "_separateScaleAxes": false, "_scaleMultiplier": {"x": 1.0, "y": 1.0, "z": 1.0}, "_randomScaleMultiplier": false, "_randomScaleMultiplierRange": {"x": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "y": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "z": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}}, "_flipX": 0, "_flipY": 0, "_thumbnailSettings": {"_backgroudColor": {"r": 0.5, "g": 0.5, "b": 0.5, "a": 1.0}, "_lightEuler": {"x": 130.0, "y": -165.0}, "_lightColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}, "_lightIntensity": 1.0, "_zoom": 1.0, "_targetEuler": {"x": 0.0, "y": 125.0, "z": 0.0}, "_targetOffset": {"x": 0.0, "y": 0.0}, "_useCustomImage": false}}}, "_brushShape": 1, "_density": 50, "_orientAlongBrushstroke": false, "_additionalOrientationAngle": {"x": 0.0, "y": 0.0, "z": 0.0}, "_spacingType": 0, "_minSpacing": 3.4843244552612305, "_randomizePositions": true, "_randomness": 1.0, "_paintOnSurfaceToolSettings": {"_paintOnMeshesWithoutCollider": false, "_paintOnSelectedOnly": false, "_paintOnPalettePrefabs": false, "_mode": 0, "_paralellToTheSurface": true}, "_maxHeightFromCenter": 2.0, "_heightType": 1, "_avoidOverlapping": 4, "_layerFilter": {"serializedVersion": "2", "m_Bits": 64}, "_tagFilter": ["Untagged", "Respawn", "Finish", "Editor<PERSON><PERSON><PERSON>", "MainCamera", "Player", "GameController"], "_slopeFilter": {"_v1": 0.0, "_v2": 60.0, "_min": 0.0, "_max": 60.0}, "_terrainLayerIds": [], "_showPreview": false}}, "_gravityToolManager": {"_profileKeys": ["<PERSON><PERSON><PERSON>"], "_profileValues": [{"_radius": 1.0, "_paintTool": {"_parentGlobalId": "", "_autoCreateParent": true, "_setSurfaceAsParent": false, "_createSubparentPerPalette": true, "_createSubparentPerTool": true, "_createSubparentPerBrush": false, "_createSubparentPerPrefab": false, "_overwritePrefabLayer": false, "_layer": 0, "_overwriteBrushProperties": false, "_brushSettings": {"_id": -1, "_surfaceDistance": 0.0, "_randomSurfaceDistance": false, "_randomSurfaceDistanceRange": {"_v1": -0.004999999888241291, "_v2": 0.004999999888241291, "_min": -0.004999999888241291, "_max": 0.004999999888241291}, "_embedInSurface": false, "_embedAtPivotHeight": true, "_localPositionOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_rotateToTheSurface": true, "_eulerOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_addRandomRotation": false, "_rotationFactor": 90.0, "_rotateInMultiples": false, "_randomEulerOffset": {"x": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "y": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "z": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}}, "_alwaysOrientUp": false, "_separateScaleAxes": false, "_scaleMultiplier": {"x": 1.0, "y": 1.0, "z": 1.0}, "_randomScaleMultiplier": false, "_randomScaleMultiplierRange": {"x": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "y": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "z": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}}, "_flipX": 0, "_flipY": 0, "_thumbnailSettings": {"_backgroudColor": {"r": 0.5, "g": 0.5, "b": 0.5, "a": 1.0}, "_lightEuler": {"x": 130.0, "y": -165.0}, "_lightColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}, "_lightIntensity": 1.0, "_zoom": 1.0, "_targetEuler": {"x": 0.0, "y": 125.0, "z": 0.0}, "_targetOffset": {"x": 0.0, "y": 0.0}, "_useCustomImage": false}}}, "_brushShape": 0, "_density": 50, "_orientAlongBrushstroke": false, "_additionalOrientationAngle": {"x": 0.0, "y": 0.0, "z": 0.0}, "_spacingType": 0, "_minSpacing": 1.0, "_randomizePositions": true, "_randomness": 1.0, "_simData": {"_maxIterations": 1000, "_gravity": {"x": 0.0, "y": -30.0, "z": 0.0}, "_drag": 0.0, "_angularDrag": 0.05000000074505806, "_maxSpeed": 100.0, "_maxAngularSpeed": 10.0, "_mass": 1.0, "_changeLayer": false, "_tempLayer": 0, "_ignoreSceneColliders": false}, "_height": 10.0, "_createTempColliders": true}], "_selectedProfileName": "<PERSON><PERSON><PERSON>", "_unsavedProfile": {"_radius": 1.0, "_paintTool": {"_parentGlobalId": "", "_autoCreateParent": true, "_setSurfaceAsParent": false, "_createSubparentPerPalette": true, "_createSubparentPerTool": true, "_createSubparentPerBrush": false, "_createSubparentPerPrefab": false, "_overwritePrefabLayer": false, "_layer": 0, "_overwriteBrushProperties": false, "_brushSettings": {"_id": -1, "_surfaceDistance": 0.0, "_randomSurfaceDistance": false, "_randomSurfaceDistanceRange": {"_v1": -0.004999999888241291, "_v2": 0.004999999888241291, "_min": -0.004999999888241291, "_max": 0.004999999888241291}, "_embedInSurface": false, "_embedAtPivotHeight": true, "_localPositionOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_rotateToTheSurface": true, "_eulerOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_addRandomRotation": false, "_rotationFactor": 90.0, "_rotateInMultiples": false, "_randomEulerOffset": {"x": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "y": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "z": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}}, "_alwaysOrientUp": false, "_separateScaleAxes": false, "_scaleMultiplier": {"x": 1.0, "y": 1.0, "z": 1.0}, "_randomScaleMultiplier": false, "_randomScaleMultiplierRange": {"x": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "y": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "z": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}}, "_flipX": 0, "_flipY": 0, "_thumbnailSettings": {"_backgroudColor": {"r": 0.5, "g": 0.5, "b": 0.5, "a": 1.0}, "_lightEuler": {"x": 130.0, "y": -165.0}, "_lightColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}, "_lightIntensity": 1.0, "_zoom": 1.0, "_targetEuler": {"x": 0.0, "y": 125.0, "z": 0.0}, "_targetOffset": {"x": 0.0, "y": 0.0}, "_useCustomImage": false}}}, "_brushShape": 0, "_density": 50, "_orientAlongBrushstroke": false, "_additionalOrientationAngle": {"x": 0.0, "y": 0.0, "z": 0.0}, "_spacingType": 0, "_minSpacing": 3.4843244552612305, "_randomizePositions": true, "_randomness": 1.0, "_simData": {"_maxIterations": 1000, "_gravity": {"x": 0.0, "y": -9.8100004196167, "z": 0.0}, "_drag": 0.0, "_angularDrag": 0.05000000074505806, "_maxSpeed": 100.0, "_maxAngularSpeed": 10.0, "_mass": 1.0, "_changeLayer": false, "_tempLayer": 0, "_ignoreSceneColliders": false}, "_height": 15.25311279296875, "_createTempColliders": true}, "_surfaceDistanceSensitivity": 1.0}, "_lineManager": {"_profileKeys": ["<PERSON><PERSON><PERSON>"], "_profileValues": [{"_paintOnMeshesWithoutCollider": false, "_paintOnSelectedOnly": false, "_paintOnPalettePrefabs": false, "_mode": 0, "_paralellToTheSurface": true, "_projectionDirection": {"x": 0.0, "y": -1.0, "z": 0.0}, "_objectsOrientedAlongTheLine": true, "_axisOrientedAlongTheLine": 0, "_spacingType": 0, "_gapSize": 0.0, "_spacing": 10.0, "_paintTool": {"_parentGlobalId": "", "_autoCreateParent": true, "_setSurfaceAsParent": false, "_createSubparentPerPalette": true, "_createSubparentPerTool": true, "_createSubparentPerBrush": false, "_createSubparentPerPrefab": false, "_overwritePrefabLayer": false, "_layer": 0, "_overwriteBrushProperties": false, "_brushSettings": {"_id": -1, "_surfaceDistance": 0.0, "_randomSurfaceDistance": false, "_randomSurfaceDistanceRange": {"_v1": -0.004999999888241291, "_v2": 0.004999999888241291, "_min": -0.004999999888241291, "_max": 0.004999999888241291}, "_embedInSurface": false, "_embedAtPivotHeight": true, "_localPositionOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_rotateToTheSurface": true, "_eulerOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_addRandomRotation": false, "_rotationFactor": 90.0, "_rotateInMultiples": false, "_randomEulerOffset": {"x": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "y": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "z": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}}, "_alwaysOrientUp": false, "_separateScaleAxes": false, "_scaleMultiplier": {"x": 1.0, "y": 1.0, "z": 1.0}, "_randomScaleMultiplier": false, "_randomScaleMultiplierRange": {"x": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "y": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "z": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}}, "_flipX": 0, "_flipY": 0, "_thumbnailSettings": {"_backgroudColor": {"r": 0.5, "g": 0.5, "b": 0.5, "a": 1.0}, "_lightEuler": {"x": 130.0, "y": -165.0}, "_lightColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}, "_lightIntensity": 1.0, "_zoom": 1.0, "_targetEuler": {"x": 0.0, "y": 125.0, "z": 0.0}, "_targetOffset": {"x": 0.0, "y": 0.0}, "_useCustomImage": false}}}}], "_selectedProfileName": "<PERSON><PERSON><PERSON>", "_unsavedProfile": {"_paintOnMeshesWithoutCollider": false, "_paintOnSelectedOnly": false, "_paintOnPalettePrefabs": false, "_mode": 0, "_paralellToTheSurface": true, "_projectionDirection": {"x": 0.0, "y": -1.0, "z": 0.0}, "_objectsOrientedAlongTheLine": true, "_axisOrientedAlongTheLine": 0, "_spacingType": 0, "_gapSize": 0.0, "_spacing": 10.0, "_paintTool": {"_parentGlobalId": "", "_autoCreateParent": true, "_setSurfaceAsParent": false, "_createSubparentPerPalette": true, "_createSubparentPerTool": true, "_createSubparentPerBrush": false, "_createSubparentPerPrefab": false, "_overwritePrefabLayer": false, "_layer": 0, "_overwriteBrushProperties": false, "_brushSettings": {"_id": -1, "_surfaceDistance": 0.0, "_randomSurfaceDistance": false, "_randomSurfaceDistanceRange": {"_v1": -0.004999999888241291, "_v2": 0.004999999888241291, "_min": -0.004999999888241291, "_max": 0.004999999888241291}, "_embedInSurface": false, "_embedAtPivotHeight": true, "_localPositionOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_rotateToTheSurface": true, "_eulerOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_addRandomRotation": false, "_rotationFactor": 90.0, "_rotateInMultiples": false, "_randomEulerOffset": {"x": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "y": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "z": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}}, "_alwaysOrientUp": false, "_separateScaleAxes": false, "_scaleMultiplier": {"x": 1.0, "y": 1.0, "z": 1.0}, "_randomScaleMultiplier": false, "_randomScaleMultiplierRange": {"x": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "y": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "z": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}}, "_flipX": 0, "_flipY": 0, "_thumbnailSettings": {"_backgroudColor": {"r": 0.5, "g": 0.5, "b": 0.5, "a": 1.0}, "_lightEuler": {"x": 130.0, "y": -165.0}, "_lightColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}, "_lightIntensity": 1.0, "_zoom": 1.0, "_targetEuler": {"x": 0.0, "y": 125.0, "z": 0.0}, "_targetOffset": {"x": 0.0, "y": 0.0}, "_useCustomImage": false}}}}, "_sceneItems": [], "_showPreexistingElements": true, "_applyBrushToExisting": false}, "_shapeManager": {"_profileKeys": ["<PERSON><PERSON><PERSON>"], "_profileValues": [{"_paintOnMeshesWithoutCollider": false, "_paintOnSelectedOnly": false, "_paintOnPalettePrefabs": false, "_mode": 0, "_paralellToTheSurface": true, "_projectionDirection": {"x": 0.0, "y": -1.0, "z": 0.0}, "_objectsOrientedAlongTheLine": true, "_axisOrientedAlongTheLine": 0, "_spacingType": 0, "_gapSize": 0.0, "_spacing": 10.0, "_paintTool": {"_parentGlobalId": "", "_autoCreateParent": true, "_setSurfaceAsParent": false, "_createSubparentPerPalette": true, "_createSubparentPerTool": true, "_createSubparentPerBrush": false, "_createSubparentPerPrefab": false, "_overwritePrefabLayer": false, "_layer": 0, "_overwriteBrushProperties": false, "_brushSettings": {"_id": -1, "_surfaceDistance": 0.0, "_randomSurfaceDistance": false, "_randomSurfaceDistanceRange": {"_v1": -0.004999999888241291, "_v2": 0.004999999888241291, "_min": -0.004999999888241291, "_max": 0.004999999888241291}, "_embedInSurface": false, "_embedAtPivotHeight": true, "_localPositionOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_rotateToTheSurface": true, "_eulerOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_addRandomRotation": false, "_rotationFactor": 90.0, "_rotateInMultiples": false, "_randomEulerOffset": {"x": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "y": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "z": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}}, "_alwaysOrientUp": false, "_separateScaleAxes": false, "_scaleMultiplier": {"x": 1.0, "y": 1.0, "z": 1.0}, "_randomScaleMultiplier": false, "_randomScaleMultiplierRange": {"x": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "y": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "z": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}}, "_flipX": 0, "_flipY": 0, "_thumbnailSettings": {"_backgroudColor": {"r": 0.5, "g": 0.5, "b": 0.5, "a": 1.0}, "_lightEuler": {"x": 130.0, "y": -165.0}, "_lightColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}, "_lightIntensity": 1.0, "_zoom": 1.0, "_targetEuler": {"x": 0.0, "y": 125.0, "z": 0.0}, "_targetOffset": {"x": 0.0, "y": 0.0}, "_useCustomImage": false}}}, "_shapeType": 1, "_sidesCount": 5, "_axisNormalToSurface": true, "_normal": {"x": 0.0, "y": 1.0, "z": 0.0}, "_projectInNormalDir": true}], "_selectedProfileName": "<PERSON><PERSON><PERSON>", "_unsavedProfile": {"_paintOnMeshesWithoutCollider": false, "_paintOnSelectedOnly": false, "_paintOnPalettePrefabs": false, "_mode": 0, "_paralellToTheSurface": true, "_projectionDirection": {"x": 0.0, "y": -1.0, "z": 0.0}, "_objectsOrientedAlongTheLine": true, "_axisOrientedAlongTheLine": 0, "_spacingType": 0, "_gapSize": 0.0, "_spacing": 10.0, "_paintTool": {"_parentGlobalId": "", "_autoCreateParent": true, "_setSurfaceAsParent": false, "_createSubparentPerPalette": true, "_createSubparentPerTool": true, "_createSubparentPerBrush": false, "_createSubparentPerPrefab": false, "_overwritePrefabLayer": false, "_layer": 0, "_overwriteBrushProperties": false, "_brushSettings": {"_id": -1, "_surfaceDistance": 0.0, "_randomSurfaceDistance": false, "_randomSurfaceDistanceRange": {"_v1": -0.004999999888241291, "_v2": 0.004999999888241291, "_min": -0.004999999888241291, "_max": 0.004999999888241291}, "_embedInSurface": false, "_embedAtPivotHeight": true, "_localPositionOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_rotateToTheSurface": true, "_eulerOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_addRandomRotation": false, "_rotationFactor": 90.0, "_rotateInMultiples": false, "_randomEulerOffset": {"x": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "y": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "z": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}}, "_alwaysOrientUp": false, "_separateScaleAxes": false, "_scaleMultiplier": {"x": 1.0, "y": 1.0, "z": 1.0}, "_randomScaleMultiplier": false, "_randomScaleMultiplierRange": {"x": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "y": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "z": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}}, "_flipX": 0, "_flipY": 0, "_thumbnailSettings": {"_backgroudColor": {"r": 0.5, "g": 0.5, "b": 0.5, "a": 1.0}, "_lightEuler": {"x": 130.0, "y": -165.0}, "_lightColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}, "_lightIntensity": 1.0, "_zoom": 1.0, "_targetEuler": {"x": 0.0, "y": 125.0, "z": 0.0}, "_targetOffset": {"x": 0.0, "y": 0.0}, "_useCustomImage": false}}}, "_shapeType": 1, "_sidesCount": 5, "_axisNormalToSurface": true, "_normal": {"x": 0.0, "y": 1.0, "z": 0.0}, "_projectInNormalDir": true}, "_sceneItems": [], "_showPreexistingElements": true, "_applyBrushToExisting": false}, "_tilingManager": {"_profileKeys": ["<PERSON><PERSON><PERSON>"], "_profileValues": [{"_paintOnMeshesWithoutCollider": false, "_paintOnSelectedOnly": false, "_paintOnPalettePrefabs": false, "_mode": 0, "_paralellToTheSurface": true, "_cellSizeType": 0, "_cellSize": {"x": 1.0, "y": 1.0}, "_rotation": {"x": 0.0, "y": 0.0, "z": 0.0, "w": 1.0}, "_spacing": {"x": 0.0, "y": 0.0}, "_axisAlignedWithNormal": {"_axis": 1, "_sign": 1}, "_showPreview": true, "_paintTool": {"_parentGlobalId": "", "_autoCreateParent": true, "_setSurfaceAsParent": false, "_createSubparentPerPalette": true, "_createSubparentPerTool": true, "_createSubparentPerBrush": false, "_createSubparentPerPrefab": false, "_overwritePrefabLayer": false, "_layer": 0, "_overwriteBrushProperties": false, "_brushSettings": {"_id": -1, "_surfaceDistance": 0.0, "_randomSurfaceDistance": false, "_randomSurfaceDistanceRange": {"_v1": -0.004999999888241291, "_v2": 0.004999999888241291, "_min": -0.004999999888241291, "_max": 0.004999999888241291}, "_embedInSurface": false, "_embedAtPivotHeight": true, "_localPositionOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_rotateToTheSurface": true, "_eulerOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_addRandomRotation": false, "_rotationFactor": 90.0, "_rotateInMultiples": false, "_randomEulerOffset": {"x": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "y": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "z": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}}, "_alwaysOrientUp": false, "_separateScaleAxes": false, "_scaleMultiplier": {"x": 1.0, "y": 1.0, "z": 1.0}, "_randomScaleMultiplier": false, "_randomScaleMultiplierRange": {"x": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "y": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "z": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}}, "_flipX": 0, "_flipY": 0, "_thumbnailSettings": {"_backgroudColor": {"r": 0.5, "g": 0.5, "b": 0.5, "a": 1.0}, "_lightEuler": {"x": 130.0, "y": -165.0}, "_lightColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}, "_lightIntensity": 1.0, "_zoom": 1.0, "_targetEuler": {"x": 0.0, "y": 125.0, "z": 0.0}, "_targetOffset": {"x": 0.0, "y": 0.0}, "_useCustomImage": false}}}}], "_selectedProfileName": "<PERSON><PERSON><PERSON>", "_unsavedProfile": {"_paintOnMeshesWithoutCollider": false, "_paintOnSelectedOnly": false, "_paintOnPalettePrefabs": false, "_mode": 0, "_paralellToTheSurface": true, "_cellSizeType": 0, "_cellSize": {"x": 1.0, "y": 1.0}, "_rotation": {"x": 0.0, "y": 0.0, "z": 0.0, "w": 1.0}, "_spacing": {"x": 0.0, "y": 0.0}, "_axisAlignedWithNormal": {"_axis": 1, "_sign": 1}, "_showPreview": true, "_paintTool": {"_parentGlobalId": "", "_autoCreateParent": true, "_setSurfaceAsParent": false, "_createSubparentPerPalette": true, "_createSubparentPerTool": true, "_createSubparentPerBrush": false, "_createSubparentPerPrefab": false, "_overwritePrefabLayer": false, "_layer": 0, "_overwriteBrushProperties": false, "_brushSettings": {"_id": -1, "_surfaceDistance": 0.0, "_randomSurfaceDistance": false, "_randomSurfaceDistanceRange": {"_v1": -0.004999999888241291, "_v2": 0.004999999888241291, "_min": -0.004999999888241291, "_max": 0.004999999888241291}, "_embedInSurface": false, "_embedAtPivotHeight": true, "_localPositionOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_rotateToTheSurface": true, "_eulerOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_addRandomRotation": false, "_rotationFactor": 90.0, "_rotateInMultiples": false, "_randomEulerOffset": {"x": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "y": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "z": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}}, "_alwaysOrientUp": false, "_separateScaleAxes": false, "_scaleMultiplier": {"x": 1.0, "y": 1.0, "z": 1.0}, "_randomScaleMultiplier": false, "_randomScaleMultiplierRange": {"x": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "y": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "z": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}}, "_flipX": 0, "_flipY": 0, "_thumbnailSettings": {"_backgroudColor": {"r": 0.5, "g": 0.5, "b": 0.5, "a": 1.0}, "_lightEuler": {"x": 130.0, "y": -165.0}, "_lightColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}, "_lightIntensity": 1.0, "_zoom": 1.0, "_targetEuler": {"x": 0.0, "y": 125.0, "z": 0.0}, "_targetOffset": {"x": 0.0, "y": 0.0}, "_useCustomImage": false}}}}, "_sceneItems": [], "_showPreexistingElements": true, "_applyBrushToExisting": false}, "_replacerManager": {"_profileKeys": ["<PERSON><PERSON><PERSON>"], "_profileValues": [{"_radius": 1.0, "_keepTargetSize": false, "_maintainProportions": false, "_positionMode": 0, "_sameParentasTarget": true, "_modifierTool": {"_command": 0, "_onlyTheClosest": false, "_outermostPrefabFilter": true, "_allButSelected": false}, "_paintTool": {"_parentGlobalId": "", "_autoCreateParent": true, "_setSurfaceAsParent": false, "_createSubparentPerPalette": true, "_createSubparentPerTool": true, "_createSubparentPerBrush": false, "_createSubparentPerPrefab": false, "_overwritePrefabLayer": false, "_layer": 0, "_overwriteBrushProperties": false, "_brushSettings": {"_id": -1, "_surfaceDistance": 0.0, "_randomSurfaceDistance": false, "_randomSurfaceDistanceRange": {"_v1": -0.004999999888241291, "_v2": 0.004999999888241291, "_min": -0.004999999888241291, "_max": 0.004999999888241291}, "_embedInSurface": false, "_embedAtPivotHeight": true, "_localPositionOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_rotateToTheSurface": true, "_eulerOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_addRandomRotation": false, "_rotationFactor": 90.0, "_rotateInMultiples": false, "_randomEulerOffset": {"x": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "y": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "z": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}}, "_alwaysOrientUp": false, "_separateScaleAxes": false, "_scaleMultiplier": {"x": 1.0, "y": 1.0, "z": 1.0}, "_randomScaleMultiplier": false, "_randomScaleMultiplierRange": {"x": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "y": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "z": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}}, "_flipX": 0, "_flipY": 0, "_thumbnailSettings": {"_backgroudColor": {"r": 0.5, "g": 0.5, "b": 0.5, "a": 1.0}, "_lightEuler": {"x": 130.0, "y": -165.0}, "_lightColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}, "_lightIntensity": 1.0, "_zoom": 1.0, "_targetEuler": {"x": 0.0, "y": 125.0, "z": 0.0}, "_targetOffset": {"x": 0.0, "y": 0.0}, "_useCustomImage": false}}}}], "_selectedProfileName": "<PERSON><PERSON><PERSON>", "_unsavedProfile": {"_radius": 1.0, "_keepTargetSize": false, "_maintainProportions": false, "_positionMode": 0, "_sameParentasTarget": true, "_modifierTool": {"_command": 0, "_onlyTheClosest": false, "_outermostPrefabFilter": true, "_allButSelected": false}, "_paintTool": {"_parentGlobalId": "", "_autoCreateParent": true, "_setSurfaceAsParent": false, "_createSubparentPerPalette": true, "_createSubparentPerTool": true, "_createSubparentPerBrush": false, "_createSubparentPerPrefab": false, "_overwritePrefabLayer": false, "_layer": 0, "_overwriteBrushProperties": false, "_brushSettings": {"_id": -1, "_surfaceDistance": 0.0, "_randomSurfaceDistance": false, "_randomSurfaceDistanceRange": {"_v1": -0.004999999888241291, "_v2": 0.004999999888241291, "_min": -0.004999999888241291, "_max": 0.004999999888241291}, "_embedInSurface": false, "_embedAtPivotHeight": true, "_localPositionOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_rotateToTheSurface": true, "_eulerOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_addRandomRotation": false, "_rotationFactor": 90.0, "_rotateInMultiples": false, "_randomEulerOffset": {"x": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "y": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "z": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}}, "_alwaysOrientUp": false, "_separateScaleAxes": false, "_scaleMultiplier": {"x": 1.0, "y": 1.0, "z": 1.0}, "_randomScaleMultiplier": false, "_randomScaleMultiplierRange": {"x": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "y": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "z": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}}, "_flipX": 0, "_flipY": 0, "_thumbnailSettings": {"_backgroudColor": {"r": 0.5, "g": 0.5, "b": 0.5, "a": 1.0}, "_lightEuler": {"x": 130.0, "y": -165.0}, "_lightColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}, "_lightIntensity": 1.0, "_zoom": 1.0, "_targetEuler": {"x": 0.0, "y": 125.0, "z": 0.0}, "_targetOffset": {"x": 0.0, "y": 0.0}, "_useCustomImage": false}}}}}, "_eraserManager": {"_profileKeys": ["<PERSON><PERSON><PERSON>"], "_profileValues": [{"_radius": 1.0, "_modifierTool": {"_command": 0, "_onlyTheClosest": false, "_outermostPrefabFilter": true, "_allButSelected": false}}], "_selectedProfileName": "<PERSON><PERSON><PERSON>", "_unsavedProfile": {"_radius": 1.0, "_modifierTool": {"_command": 0, "_onlyTheClosest": false, "_outermostPrefabFilter": true, "_allButSelected": false}}}, "_selectionToolManager": {"_profileKeys": ["<PERSON><PERSON><PERSON>"], "_profileValues": [{"_embedInSurface": false, "_embedAtPivotHeight": false, "_surfaceDistance": 0.0, "_createTempColliders": true, "_rotateToTheSurface": false, "_move": true, "_rotate": false, "_scale": false, "_handleSpace": 1, "_boxSpace": 1, "_paletteFilter": false, "_brushFilter": false, "_layerFilter": {"serializedVersion": "2", "m_Bits": 4294967295}, "_tagFilter": ["Untagged", "Respawn", "Finish", "Editor<PERSON><PERSON><PERSON>", "MainCamera", "Player", "GameController", "AI", "ignoreSensor", "Ground", "carBody", "CheckPoint"]}], "_selectedProfileName": "<PERSON><PERSON><PERSON>", "_unsavedProfile": {"_embedInSurface": false, "_embedAtPivotHeight": false, "_surfaceDistance": 0.0, "_createTempColliders": true, "_rotateToTheSurface": false, "_move": true, "_rotate": false, "_scale": false, "_handleSpace": 1, "_boxSpace": 1, "_paletteFilter": false, "_brushFilter": false, "_layerFilter": {"serializedVersion": "2", "m_Bits": 4294967295}, "_tagFilter": ["Untagged", "Respawn", "Finish", "Editor<PERSON><PERSON><PERSON>", "MainCamera", "Player", "GameController"]}}, "_circleSelectToolManager": {"_profileKeys": ["<PERSON><PERSON><PERSON>"], "_profileValues": [{"_radius": 1.0, "_modifierTool": {"_command": 0, "_onlyTheClosest": false, "_outermostPrefabFilter": true, "_allButSelected": false}}], "_selectedProfileName": "<PERSON><PERSON><PERSON>", "_unsavedProfile": {"_radius": 1.0, "_modifierTool": {"_command": 0, "_onlyTheClosest": false, "_outermostPrefabFilter": true, "_allButSelected": false}}}, "_extrudeSettings": {"_profileKeys": ["<PERSON><PERSON><PERSON>"], "_profileValues": [{"_embedInSurface": false, "_embedAtPivotHeight": false, "_surfaceDistance": 0.0, "_createTempColliders": true, "_space": 0, "_spacing": {"x": 0.0, "y": 0.0, "z": 0.0}, "_spacingType": 1, "_multiplier": {"x": 1.0, "y": 1.0, "z": 1.0}, "_rotationAccordingTo": 0, "_sameParentAsSource": true, "_eulerOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_addRandomRotation": false, "_rotationFactor": 90.0, "_rotateInMultiples": false, "_randomEulerOffset": {"x": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "y": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "z": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}}, "_paintTool": {"_parentGlobalId": "", "_autoCreateParent": true, "_setSurfaceAsParent": false, "_createSubparentPerPalette": true, "_createSubparentPerTool": true, "_createSubparentPerBrush": false, "_createSubparentPerPrefab": false, "_overwritePrefabLayer": false, "_layer": 0, "_overwriteBrushProperties": false, "_brushSettings": {"_id": -1, "_surfaceDistance": 0.0, "_randomSurfaceDistance": false, "_randomSurfaceDistanceRange": {"_v1": -0.004999999888241291, "_v2": 0.004999999888241291, "_min": -0.004999999888241291, "_max": 0.004999999888241291}, "_embedInSurface": false, "_embedAtPivotHeight": true, "_localPositionOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_rotateToTheSurface": true, "_eulerOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_addRandomRotation": false, "_rotationFactor": 90.0, "_rotateInMultiples": false, "_randomEulerOffset": {"x": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "y": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "z": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}}, "_alwaysOrientUp": false, "_separateScaleAxes": false, "_scaleMultiplier": {"x": 1.0, "y": 1.0, "z": 1.0}, "_randomScaleMultiplier": false, "_randomScaleMultiplierRange": {"x": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "y": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "z": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}}, "_flipX": 0, "_flipY": 0, "_thumbnailSettings": {"_backgroudColor": {"r": 0.5, "g": 0.5, "b": 0.5, "a": 1.0}, "_lightEuler": {"x": 130.0, "y": -165.0}, "_lightColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}, "_lightIntensity": 1.0, "_zoom": 1.0, "_targetEuler": {"x": 0.0, "y": 125.0, "z": 0.0}, "_targetOffset": {"x": 0.0, "y": 0.0}, "_useCustomImage": false}}}}], "_selectedProfileName": "<PERSON><PERSON><PERSON>", "_unsavedProfile": {"_embedInSurface": false, "_embedAtPivotHeight": false, "_surfaceDistance": 0.0, "_createTempColliders": true, "_space": 0, "_spacing": {"x": 0.0, "y": 0.0, "z": 0.0}, "_spacingType": 1, "_multiplier": {"x": 1.0, "y": 1.0, "z": 1.0}, "_rotationAccordingTo": 0, "_sameParentAsSource": true, "_eulerOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_addRandomRotation": false, "_rotationFactor": 90.0, "_rotateInMultiples": false, "_randomEulerOffset": {"x": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "y": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "z": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}}, "_paintTool": {"_parentGlobalId": "", "_autoCreateParent": true, "_setSurfaceAsParent": false, "_createSubparentPerPalette": true, "_createSubparentPerTool": true, "_createSubparentPerBrush": false, "_createSubparentPerPrefab": false, "_overwritePrefabLayer": false, "_layer": 0, "_overwriteBrushProperties": false, "_brushSettings": {"_id": -1, "_surfaceDistance": 0.0, "_randomSurfaceDistance": false, "_randomSurfaceDistanceRange": {"_v1": -0.004999999888241291, "_v2": 0.004999999888241291, "_min": -0.004999999888241291, "_max": 0.004999999888241291}, "_embedInSurface": false, "_embedAtPivotHeight": true, "_localPositionOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_rotateToTheSurface": true, "_eulerOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_addRandomRotation": false, "_rotationFactor": 90.0, "_rotateInMultiples": false, "_randomEulerOffset": {"x": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "y": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "z": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}}, "_alwaysOrientUp": false, "_separateScaleAxes": false, "_scaleMultiplier": {"x": 1.0, "y": 1.0, "z": 1.0}, "_randomScaleMultiplier": false, "_randomScaleMultiplierRange": {"x": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "y": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "z": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}}, "_flipX": 0, "_flipY": 0, "_thumbnailSettings": {"_backgroudColor": {"r": 0.5, "g": 0.5, "b": 0.5, "a": 1.0}, "_lightEuler": {"x": 130.0, "y": -165.0}, "_lightColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}, "_lightIntensity": 1.0, "_zoom": 1.0, "_targetEuler": {"x": 0.0, "y": 125.0, "z": 0.0}, "_targetOffset": {"x": 0.0, "y": 0.0}, "_useCustomImage": false}}}}}, "_mirrorManager": {"_profileKeys": ["<PERSON><PERSON><PERSON>"], "_profileValues": [{"_embedInSurface": false, "_embedAtPivotHeight": false, "_surfaceDistance": 0.0, "_createTempColliders": true, "_rotateToTheSurface": false, "_reflectRotation": true, "_action": 1, "_sameParentAsSource": true, "_mirrorPose": {"position": {"x": 0.0, "y": 0.0, "z": 0.0}, "rotation": {"x": 0.0, "y": 0.7071068286895752, "z": 0.0, "w": 0.7071068286895752}}, "_invertScale": false, "_paintTool": {"_parentGlobalId": "", "_autoCreateParent": true, "_setSurfaceAsParent": false, "_createSubparentPerPalette": true, "_createSubparentPerTool": true, "_createSubparentPerBrush": false, "_createSubparentPerPrefab": false, "_overwritePrefabLayer": false, "_layer": 0, "_overwriteBrushProperties": false, "_brushSettings": {"_id": -1, "_surfaceDistance": 0.0, "_randomSurfaceDistance": false, "_randomSurfaceDistanceRange": {"_v1": -0.004999999888241291, "_v2": 0.004999999888241291, "_min": -0.004999999888241291, "_max": 0.004999999888241291}, "_embedInSurface": false, "_embedAtPivotHeight": true, "_localPositionOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_rotateToTheSurface": true, "_eulerOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_addRandomRotation": false, "_rotationFactor": 90.0, "_rotateInMultiples": false, "_randomEulerOffset": {"x": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "y": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "z": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}}, "_alwaysOrientUp": false, "_separateScaleAxes": false, "_scaleMultiplier": {"x": 1.0, "y": 1.0, "z": 1.0}, "_randomScaleMultiplier": false, "_randomScaleMultiplierRange": {"x": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "y": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "z": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}}, "_flipX": 0, "_flipY": 0, "_thumbnailSettings": {"_backgroudColor": {"r": 0.5, "g": 0.5, "b": 0.5, "a": 1.0}, "_lightEuler": {"x": 130.0, "y": -165.0}, "_lightColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}, "_lightIntensity": 1.0, "_zoom": 1.0, "_targetEuler": {"x": 0.0, "y": 125.0, "z": 0.0}, "_targetOffset": {"x": 0.0, "y": 0.0}, "_useCustomImage": false}}}}], "_selectedProfileName": "<PERSON><PERSON><PERSON>", "_unsavedProfile": {"_embedInSurface": false, "_embedAtPivotHeight": false, "_surfaceDistance": 0.0, "_createTempColliders": true, "_rotateToTheSurface": false, "_reflectRotation": true, "_action": 1, "_sameParentAsSource": true, "_mirrorPose": {"position": {"x": 0.0, "y": 0.0, "z": 0.0}, "rotation": {"x": 0.0, "y": 0.7071068286895752, "z": 0.0, "w": 0.7071068286895752}}, "_invertScale": false, "_paintTool": {"_parentGlobalId": "", "_autoCreateParent": true, "_setSurfaceAsParent": false, "_createSubparentPerPalette": true, "_createSubparentPerTool": true, "_createSubparentPerBrush": false, "_createSubparentPerPrefab": false, "_overwritePrefabLayer": false, "_layer": 0, "_overwriteBrushProperties": false, "_brushSettings": {"_id": -1, "_surfaceDistance": 0.0, "_randomSurfaceDistance": false, "_randomSurfaceDistanceRange": {"_v1": -0.004999999888241291, "_v2": 0.004999999888241291, "_min": -0.004999999888241291, "_max": 0.004999999888241291}, "_embedInSurface": false, "_embedAtPivotHeight": true, "_localPositionOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_rotateToTheSurface": true, "_eulerOffset": {"x": 0.0, "y": 0.0, "z": 0.0}, "_addRandomRotation": false, "_rotationFactor": 90.0, "_rotateInMultiples": false, "_randomEulerOffset": {"x": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "y": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}, "z": {"_v1": 0.0, "_v2": 0.0, "_min": 0.0, "_max": 0.0}}, "_alwaysOrientUp": false, "_separateScaleAxes": false, "_scaleMultiplier": {"x": 1.0, "y": 1.0, "z": 1.0}, "_randomScaleMultiplier": false, "_randomScaleMultiplierRange": {"x": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "y": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}, "z": {"_v1": 1.0, "_v2": 1.0, "_min": 1.0, "_max": 1.0}}, "_flipX": 0, "_flipY": 0, "_thumbnailSettings": {"_backgroudColor": {"r": 0.5, "g": 0.5, "b": 0.5, "a": 1.0}, "_lightEuler": {"x": 130.0, "y": -165.0}, "_lightColor": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}, "_lightIntensity": 1.0, "_zoom": 1.0, "_targetEuler": {"x": 0.0, "y": 125.0, "z": 0.0}, "_targetOffset": {"x": 0.0, "y": 0.0}, "_useCustomImage": false}}}}}, "_snapManager": {"_settings": {"_snappingEnabled": false, "_snappingOn": {"x": true, "y": true, "z": false}, "_visibleGrid": false, "_gridOn": {"x": false, "y": false, "z": true}, "_lockedGrid": false, "_step": {"x": 2.0, "y": 1.6499998569488525, "z": 2.0}, "_origin": {"x": 231.68069458007812, "y": -64.7400131225586, "z": -1099.3807373046875}, "_rotation": {"x": 0.0, "y": 0.0, "z": 0.0, "w": 1.0}, "_showPositionHandle": true, "_showRotationHandle": false, "_showScaleHandle": false, "_radialGridEnabled": false, "_radialStep": 1.0, "_radialSectors": 8, "_snapToRadius": true, "_snapToCircunference": true, "_majorLinesGap": {"x": 10, "y": 10, "z": 10}, "_midpointSnapping": false, "_origins": [{"_name": "<PERSON><PERSON><PERSON>", "_pose": {"position": {"x": 0.0, "y": 0.0, "z": 0.0}, "rotation": {"x": 0.0, "y": 0.0, "z": 0.0, "w": 1.0}}}], "_selectedOrigin": "<PERSON><PERSON><PERSON>"}}}