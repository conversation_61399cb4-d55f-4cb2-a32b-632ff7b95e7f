using UnityEngine;
using UnityEditor;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

namespace Performance.Editor
{
    /// <summary>
    /// 性能优化工具 - 一键设置120FPS优化配置
    /// </summary>
    public class PerformanceOptimizationTool : EditorWindow
    {
        #region Private Fields
        private bool m_Enable120FPS = true;
        private bool m_EnableMobileOptimizations = true;
        private bool m_EnableAdaptiveQuality = true;
        private bool m_OptimizeForBattery = false;
        private float m_RenderScale = 1.0f;
        private int m_ShadowQuality = 2;
        private bool m_ShowAdvancedSettings = false;
        
        private Vector2 m_ScrollPosition;
        #endregion

        #region Menu Item
        [MenuItem("Tools/Performance/120FPS Optimization Tool")]
        public static void ShowWindow()
        {
            var window = GetWindow<PerformanceOptimizationTool>("120FPS优化工具");
            window.minSize = new Vector2(400, 600);
            window.Show();
        }
        #endregion

        #region GUI
        private void OnGUI()
        {
            m_ScrollPosition = EditorGUILayout.BeginScrollView(m_ScrollPosition);
            
            DrawHeader();
            DrawMainSettings();
            DrawMobileSettings();
            DrawAdvancedSettings();
            DrawActionButtons();
            DrawCurrentStatus();
            
            EditorGUILayout.EndScrollView();
        }

        private void DrawHeader()
        {
            EditorGUILayout.Space(10);
            
            GUIStyle headerStyle = new GUIStyle(EditorStyles.boldLabel);
            headerStyle.fontSize = 16;
            headerStyle.alignment = TextAnchor.MiddleCenter;
            
            EditorGUILayout.LabelField("Unity 120FPS 性能优化工具", headerStyle);
            EditorGUILayout.LabelField("一键优化您的赛车游戏以达到120帧", EditorStyles.centeredGreyMiniLabel);
            
            EditorGUILayout.Space(10);
            EditorGUILayout.LabelField("", GUI.skin.horizontalSlider);
        }

        private void DrawMainSettings()
        {
            EditorGUILayout.LabelField("主要设置", EditorStyles.boldLabel);
            
            m_Enable120FPS = EditorGUILayout.Toggle(new GUIContent("启用120FPS模式", 
                "设置目标帧率为120并禁用垂直同步"), m_Enable120FPS);
            
            m_EnableAdaptiveQuality = EditorGUILayout.Toggle(new GUIContent("启用自适应质量", 
                "根据性能自动调整质量设置"), m_EnableAdaptiveQuality);
            
            m_RenderScale = EditorGUILayout.Slider(new GUIContent("渲染分辨率缩放", 
                "降低渲染分辨率以提高性能"), m_RenderScale, 0.5f, 1.0f);
            
            m_ShadowQuality = EditorGUILayout.IntSlider(new GUIContent("阴影质量等级", 
                "0=禁用, 1=硬阴影, 2=软阴影, 3=高质量"), m_ShadowQuality, 0, 3);
            
            EditorGUILayout.Space(10);
        }

        private void DrawMobileSettings()
        {
            EditorGUILayout.LabelField("移动设备设置", EditorStyles.boldLabel);
            
            m_EnableMobileOptimizations = EditorGUILayout.Toggle(new GUIContent("启用移动设备优化", 
                "应用专门针对移动设备的优化设置"), m_EnableMobileOptimizations);
            
            if (m_EnableMobileOptimizations)
            {
                EditorGUI.indentLevel++;
                
                m_OptimizeForBattery = EditorGUILayout.Toggle(new GUIContent("电池优化模式", 
                    "进一步降低功耗，可能影响性能"), m_OptimizeForBattery);
                
                EditorGUI.indentLevel--;
            }
            
            EditorGUILayout.Space(10);
        }

        private void DrawAdvancedSettings()
        {
            m_ShowAdvancedSettings = EditorGUILayout.Foldout(m_ShowAdvancedSettings, "高级设置", true);
            
            if (m_ShowAdvancedSettings)
            {
                EditorGUI.indentLevel++;
                
                EditorGUILayout.HelpBox("高级设置允许您微调性能参数。请谨慎修改这些设置。", MessageType.Info);
                
                EditorGUILayout.LabelField("物理设置");
                EditorGUI.indentLevel++;
                EditorGUILayout.LabelField($"当前固定时间步长: {Time.fixedDeltaTime:F4}s ({1.0f/Time.fixedDeltaTime:F0}Hz)");
                if (GUILayout.Button("优化物理设置 (60Hz)"))
                {
                    OptimizePhysicsSettings();
                }
                EditorGUI.indentLevel--;
                
                EditorGUILayout.Space(5);
                
                EditorGUILayout.LabelField("内存设置");
                EditorGUI.indentLevel++;
                if (GUILayout.Button("优化内存设置"))
                {
                    OptimizeMemorySettings();
                }
                EditorGUI.indentLevel--;
                
                EditorGUI.indentLevel--;
            }
            
            EditorGUILayout.Space(10);
        }

        private void DrawActionButtons()
        {
            EditorGUILayout.LabelField("操作", EditorStyles.boldLabel);
            
            GUIStyle buttonStyle = new GUIStyle(GUI.skin.button);
            buttonStyle.fontSize = 12;
            buttonStyle.fixedHeight = 30;
            
            if (GUILayout.Button("🚀 应用120FPS优化", buttonStyle))
            {
                Apply120FPSOptimizations();
            }
            
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button("📱 应用移动设备优化", buttonStyle))
            {
                ApplyMobileOptimizations();
            }
            
            if (GUILayout.Button("🔄 恢复默认设置", buttonStyle))
            {
                RestoreDefaultSettings();
            }
            
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space(5);
            
            if (GUILayout.Button("📊 创建性能监控器", buttonStyle))
            {
                CreatePerformanceMonitor();
            }
            
            EditorGUILayout.Space(10);
        }

        private void DrawCurrentStatus()
        {
            EditorGUILayout.LabelField("当前状态", EditorStyles.boldLabel);
            
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            
            EditorGUILayout.LabelField($"目标帧率: {Application.targetFrameRate}");
            EditorGUILayout.LabelField($"垂直同步: {(QualitySettings.vSyncCount > 0 ? "开启" : "关闭")}");
            EditorGUILayout.LabelField($"当前质量等级: {QualitySettings.names[QualitySettings.GetQualityLevel()]}");
            EditorGUILayout.LabelField($"阴影质量: {QualitySettings.shadows}");
            EditorGUILayout.LabelField($"物理更新频率: {1.0f/Time.fixedDeltaTime:F0}Hz");
            
            // URP信息
            var urpAsset = GraphicsSettings.currentRenderPipeline as UniversalRenderPipelineAsset;
            if (urpAsset != null)
            {
                EditorGUILayout.LabelField($"URP渲染缩放: {urpAsset.renderScale:F2}");
            }
            else
            {
                EditorGUILayout.LabelField("渲染管线: 内置渲染管线");
            }
            
            EditorGUILayout.EndVertical();
        }
        #endregion

        #region Optimization Methods
        private void Apply120FPSOptimizations()
        {
            Debug.Log("[PerformanceOptimizationTool] 开始应用120FPS优化...");
            
            // 设置目标帧率
            if (m_Enable120FPS)
            {
                Application.targetFrameRate = 120;
                QualitySettings.vSyncCount = 0;
                Debug.Log("✓ 目标帧率设置为120FPS，垂直同步已禁用");
            }
            else
            {
                Application.targetFrameRate = 60;
                QualitySettings.vSyncCount = 1;
                Debug.Log("✓ 目标帧率设置为60FPS，垂直同步已启用");
            }
            
            // 优化渲染设置
            OptimizeRenderingSettings();
            
            // 优化阴影设置
            OptimizeShadowSettings();
            
            // 优化物理设置
            OptimizePhysicsSettings();
            
            // 优化质量设置
            OptimizeQualitySettings();
            
            Debug.Log("🚀 120FPS优化应用完成！");
            ShowNotification(new GUIContent("120FPS优化已应用！"));
        }

        private void ApplyMobileOptimizations()
        {
            if (!m_EnableMobileOptimizations) return;
            
            Debug.Log("[PerformanceOptimizationTool] 开始应用移动设备优化...");
            
            // 移动设备特定优化
            QualitySettings.globalTextureMipmapLimit = 1;
            QualitySettings.anisotropicFiltering = AnisotropicFiltering.Disable;
            QualitySettings.realtimeReflectionProbes = false;
            QualitySettings.billboardsFaceCameraPosition = false;
            QualitySettings.particleRaycastBudget = 16;
            QualitySettings.lodBias = 0.7f;
            
            // 启用纹理流送
            QualitySettings.streamingMipmapsActive = true;
            QualitySettings.streamingMipmapsMemoryBudget = 256;
            QualitySettings.streamingMipmapsMaxLevelReduction = 2;
            
            // 电池优化
            if (m_OptimizeForBattery)
            {
                Time.fixedDeltaTime = 1.0f / 30.0f; // 30Hz物理更新
                m_RenderScale = Mathf.Min(m_RenderScale, 0.8f);
                Debug.Log("✓ 电池优化模式已启用");
            }
            
            Debug.Log("📱 移动设备优化应用完成！");
            ShowNotification(new GUIContent("移动设备优化已应用！"));
        }

        private void OptimizeRenderingSettings()
        {
            var urpAsset = GraphicsSettings.currentRenderPipeline as UniversalRenderPipelineAsset;
            if (urpAsset != null)
            {
                urpAsset.renderScale = m_RenderScale;
                Debug.Log($"✓ URP渲染分辨率缩放设置为 {m_RenderScale:F2}");
            }
            else
            {
                Debug.LogWarning("⚠️ 未检测到URP，某些渲染优化可能不可用");
            }
        }

        private void OptimizeShadowSettings()
        {
            switch (m_ShadowQuality)
            {
                case 0:
                    QualitySettings.shadows = ShadowQuality.Disable;
                    Debug.Log("✓ 阴影已禁用");
                    break;
                case 1:
                    QualitySettings.shadows = ShadowQuality.HardOnly;
                    QualitySettings.shadowResolution = ShadowResolution.Low;
                    QualitySettings.shadowDistance = 30f;
                    Debug.Log("✓ 阴影设置为硬阴影，低分辨率");
                    break;
                case 2:
                    QualitySettings.shadows = ShadowQuality.All;
                    QualitySettings.shadowResolution = ShadowResolution.Medium;
                    QualitySettings.shadowDistance = 50f;
                    Debug.Log("✓ 阴影设置为软阴影，中等分辨率");
                    break;
                case 3:
                    QualitySettings.shadows = ShadowQuality.All;
                    QualitySettings.shadowResolution = ShadowResolution.High;
                    QualitySettings.shadowDistance = 80f;
                    Debug.Log("✓ 阴影设置为高质量");
                    break;
            }
        }

        private void OptimizePhysicsSettings()
        {
            if (m_Enable120FPS)
            {
                Time.fixedDeltaTime = 1.0f / 60.0f; // 60Hz物理更新
                Physics.defaultSolverIterations = 4;
                Physics.defaultSolverVelocityIterations = 1;
                Debug.Log("✓ 物理设置已优化为60Hz更新频率");
            }
        }

        private void OptimizeQualitySettings()
        {
            // 异步上传优化
            QualitySettings.asyncUploadTimeSlice = 4;
            QualitySettings.asyncUploadBufferSize = 32;
            QualitySettings.asyncUploadPersistentBuffer = true;
            
            Debug.Log("✓ 质量设置已优化");
        }

        private void OptimizeMemorySettings()
        {
            QualitySettings.streamingMipmapsActive = true;
            QualitySettings.streamingMipmapsMemoryBudget = 512;
            QualitySettings.streamingMipmapsMaxLevelReduction = 2;
            QualitySettings.streamingMipmapsMaxFileIORequests = 1024;
            
            Debug.Log("✓ 内存设置已优化");
            ShowNotification(new GUIContent("内存设置已优化！"));
        }

        private void RestoreDefaultSettings()
        {
            if (EditorUtility.DisplayDialog("恢复默认设置", 
                "确定要恢复所有设置到默认值吗？这将撤销所有优化。", "确定", "取消"))
            {
                Application.targetFrameRate = -1;
                QualitySettings.vSyncCount = 1;
                Time.fixedDeltaTime = 0.02f; // 50Hz
                
                var urpAsset = GraphicsSettings.currentRenderPipeline as UniversalRenderPipelineAsset;
                if (urpAsset != null)
                {
                    urpAsset.renderScale = 1.0f;
                }
                
                QualitySettings.shadows = ShadowQuality.All;
                QualitySettings.shadowResolution = ShadowResolution.Medium;
                
                Debug.Log("🔄 设置已恢复到默认值");
                ShowNotification(new GUIContent("设置已恢复到默认值！"));
            }
        }

        private void CreatePerformanceMonitor()
        {
            // 查找是否已存在性能管理器
            var existingManager = FindObjectOfType<Performance.PerformanceManager>();
            if (existingManager != null)
            {
                Debug.Log("⚠️ 场景中已存在PerformanceManager");
                ShowNotification(new GUIContent("性能管理器已存在！"));
                return;
            }
            
            // 创建性能管理器GameObject
            GameObject performanceManagerGO = new GameObject("PerformanceManager");
            performanceManagerGO.AddComponent<Performance.PerformanceManager>();
            
            // 如果是移动平台，也添加移动优化器
            if (EditorUserBuildSettings.activeBuildTarget == BuildTarget.Android ||
                EditorUserBuildSettings.activeBuildTarget == BuildTarget.iOS)
            {
                performanceManagerGO.AddComponent<Performance.MobilePerformanceOptimizer>();
            }
            
            // 选中新创建的对象
            Selection.activeGameObject = performanceManagerGO;
            
            Debug.Log("📊 性能监控器已创建");
            ShowNotification(new GUIContent("性能监控器已创建！"));
        }
        #endregion
    }
}
