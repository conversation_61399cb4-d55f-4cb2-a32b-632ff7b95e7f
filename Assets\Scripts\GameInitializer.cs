using UnityEngine;
using Performance;

/// <summary>
/// 游戏初始化器 - 负责在游戏启动时自动设置性能优化
/// </summary>
public class GameInitializer : MonoBehaviour
{
    #region Serialized Fields
    [Header("性能设置")]
    [SerializeField, <PERSON>lt<PERSON>("自动启用120FPS优化")]
    private bool m_AutoEnable120FPS = true;

    [SerializeField, Toolt<PERSON>("自动创建性能管理器")]
    private bool m_AutoCreatePerformanceManager = true;

    [SerializeField, Tooltip("自动应用移动设备优化")]
    private bool m_AutoApplyMobileOptimizations = true;

    [Header("调试设置")]
    [SerializeField, Tooltip("显示初始化日志")]
    private bool m_ShowInitializationLogs = true;

    [SerializeField, Tooltip("在移动设备上显示性能监控")]
    private bool m_ShowPerformanceMonitorOnMobile = true;
    #endregion

    #region Private Fields
    private static bool s_IsInitialized = false;
    #endregion

    #region Unity Lifecycle
    private void Awake()
    {
        // 确保只初始化一次
        if (s_IsInitialized)
        {
            if (m_ShowInitializationLogs)
                Debug.Log("[GameInitializer] 游戏已初始化，跳过重复初始化");
            return;
        }

        InitializeGame();
        s_IsInitialized = true;
    }
    #endregion

    #region Initialization
    private void InitializeGame()
    {
        if (m_ShowInitializationLogs)
            Debug.Log("[GameInitializer] 开始游戏初始化...");

        // 设置基本性能参数
        SetupBasicPerformanceSettings();

        // 创建性能管理器
        if (m_AutoCreatePerformanceManager)
        {
            CreatePerformanceManager();
        }

        // 应用移动设备优化
        if (m_AutoApplyMobileOptimizations && IsMobileDevice())
        {
            ApplyMobileOptimizations();
        }

        // 应用120FPS设置
        if (m_AutoEnable120FPS)
        {
            Enable120FPS();
        }

        if (m_ShowInitializationLogs)
            Debug.Log("[GameInitializer] 游戏初始化完成！");
    }

    private void SetupBasicPerformanceSettings()
    {
        // 设置应用程序在后台运行
        Application.runInBackground = false;

        // 设置屏幕不休眠（移动设备）
        if (IsMobileDevice())
        {
            Screen.sleepTimeout = SleepTimeout.NeverSleep;
        }

        // 优化垃圾回收
        System.GC.Collect();

        if (m_ShowInitializationLogs)
            Debug.Log("[GameInitializer] ✓ 基本性能设置已配置");
    }

    private void CreatePerformanceManager()
    {
        // 检查是否已存在性能管理器
        var existingManager = FindObjectOfType<PerformanceManager>();
        if (existingManager != null)
        {
            if (m_ShowInitializationLogs)
                Debug.Log("[GameInitializer] 性能管理器已存在，跳过创建");
            return;
        }

        // 创建性能管理器
        GameObject performanceManagerGO = new GameObject("PerformanceManager");
        DontDestroyOnLoad(performanceManagerGO);

        var performanceManager = performanceManagerGO.AddComponent<PerformanceManager>();

        // 如果是移动设备，添加移动优化器
        if (IsMobileDevice())
        {
            performanceManagerGO.AddComponent<MobilePerformanceOptimizer>();
        }

        if (m_ShowInitializationLogs)
            Debug.Log("[GameInitializer] ✓ 性能管理器已创建");
    }

    private void ApplyMobileOptimizations()
    {
        if (m_ShowInitializationLogs)
            Debug.Log("[GameInitializer] 应用移动设备优化...");

        // 移动设备特定设置
        QualitySettings.globalTextureMipmapLimit = 1;
        QualitySettings.anisotropicFiltering = AnisotropicFiltering.Disable;
        QualitySettings.realtimeReflectionProbes = false;
        QualitySettings.billboardsFaceCameraPosition = false;
        QualitySettings.particleRaycastBudget = 16;
        QualitySettings.lodBias = 0.7f;

        // 启用纹理流送
        QualitySettings.streamingMipmapsActive = true;
        QualitySettings.streamingMipmapsMemoryBudget = 256;
        QualitySettings.streamingMipmapsMaxLevelReduction = 2;

        // 优化异步上传
        QualitySettings.asyncUploadTimeSlice = 4;
        QualitySettings.asyncUploadBufferSize = 32;
        QualitySettings.asyncUploadPersistentBuffer = true;

        // 优化阴影设置
        QualitySettings.shadows = ShadowQuality.HardOnly;
        QualitySettings.shadowResolution = ShadowResolution.Low;
        QualitySettings.shadowDistance = 30f;

        if (m_ShowInitializationLogs)
            Debug.Log("[GameInitializer] ✓ 移动设备优化已应用");
    }

    private void Enable120FPS()
    {
        // 设置目标帧率
        Application.targetFrameRate = 120;

        // 禁用垂直同步以允许高帧率
        QualitySettings.vSyncCount = 0;

        // 优化物理设置
        Time.fixedDeltaTime = 1.0f / 60.0f; // 60Hz物理更新
        Physics.defaultSolverIterations = 4;
        Physics.defaultSolverVelocityIterations = 1;

        if (m_ShowInitializationLogs)
            Debug.Log("[GameInitializer] ✓ 120FPS模式已启用");
    }

    private bool IsMobileDevice()
    {
        return Application.platform == RuntimePlatform.Android || 
               Application.platform == RuntimePlatform.IPhonePlayer;
    }
    #endregion

    #region Public Methods
    /// <summary>
    /// 手动重新初始化游戏设置
    /// </summary>
    public void ReinitializeGame()
    {
        s_IsInitialized = false;
        InitializeGame();
        s_IsInitialized = true;
    }

    /// <summary>
    /// 切换120FPS模式
    /// </summary>
    public void Toggle120FPS(bool enable)
    {
        if (enable)
        {
            Application.targetFrameRate = 120;
            QualitySettings.vSyncCount = 0;
        }
        else
        {
            Application.targetFrameRate = 60;
            QualitySettings.vSyncCount = 1;
        }

        if (m_ShowInitializationLogs)
            Debug.Log($"[GameInitializer] 120FPS模式已{(enable ? "启用" : "禁用")}");
    }

    /// <summary>
    /// 获取当前性能状态
    /// </summary>
    public string GetPerformanceStatus()
    {
        return $"目标帧率: {Application.targetFrameRate} | " +
               $"垂直同步: {(QualitySettings.vSyncCount > 0 ? "开启" : "关闭")} | " +
               $"质量等级: {QualitySettings.names[QualitySettings.GetQualityLevel()]} | " +
               $"物理频率: {1.0f/Time.fixedDeltaTime:F0}Hz";
    }
    #endregion

    #region Static Methods
    /// <summary>
    /// 静态方法：快速启用120FPS优化
    /// </summary>
    [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.BeforeSceneLoad)]
    public static void QuickEnable120FPS()
    {
        // 在场景加载前快速设置基本的120FPS参数
        Application.targetFrameRate = 120;
        QualitySettings.vSyncCount = 0;
        
        Debug.Log("[GameInitializer] 快速120FPS设置已应用");
    }
    #endregion
}
