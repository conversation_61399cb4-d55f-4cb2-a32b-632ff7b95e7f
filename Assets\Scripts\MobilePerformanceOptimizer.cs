using UnityEngine;
using UnityEngine.Rendering;
using System.Collections;

namespace Performance
{
    /// <summary>
    /// 移动设备性能优化器
    /// 专门针对Android和iOS设备的CPU/GPU使用率优化
    /// </summary>
    public class MobilePerformanceOptimizer : MonoBehaviour
    {
        #region Constants
        private const float c_ThermalCheckInterval = 5.0f;
        private const float c_BatteryCheckInterval = 10.0f;
        private const int c_MaxCPUCores = 8;
        #endregion

        #region Serialized Fields
        [Header("CPU优化")]
        [SerializeField, Tooltip("启用多线程渲染")]
        private bool m_EnableMultithreadedRendering = true;

        [SerializeField, Tooltip("目标CPU使用率 (0-1)")]
        [Range(0.3f, 0.9f)]
        private float m_TargetCPUUsage = 0.7f;

        [SerializeField, Tooltip("启用CPU频率优化")]
        private bool m_EnableCPUFrequencyOptimization = true;

        [Header("GPU优化")]
        [SerializeField, Tooltip("启用GPU频率优化")]
        private bool m_EnableGPUOptimization = true;

        [SerializeField, Tooltip("目标GPU使用率 (0-1)")]
        [Range(0.5f, 0.95f)]
        private float m_TargetGPUUsage = 0.8f;

        [SerializeField, Tooltip("动态分辨率调整")]
        private bool m_EnableDynamicResolution = true;

        [Header("热管理")]
        [SerializeField, Tooltip("启用热管理")]
        private bool m_EnableThermalManagement = true;

        [SerializeField, Tooltip("热保护阈值温度")]
        [Range(35f, 50f)]
        private float m_ThermalThreshold = 42f;

        [Header("电池优化")]
        [SerializeField, Tooltip("启用电池感知优化")]
        private bool m_EnableBatteryAwareOptimization = true;

        [SerializeField, Tooltip("低电量阈值")]
        [Range(0.1f, 0.3f)]
        private float m_LowBatteryThreshold = 0.2f;
        #endregion

        #region Private Fields
        private bool m_IsInitialized;
        private float m_LastThermalCheck;
        private float m_LastBatteryCheck;
        private bool m_IsThermalThrottling;
        private bool m_IsLowBattery;
        private int m_OriginalTargetFrameRate;
        private float m_OriginalRenderScale = 1.0f;
        
        // 性能监控
        private float m_CurrentCPUUsage;
        private float m_CurrentGPUUsage;
        private float m_DeviceTemperature;
        private float m_BatteryLevel;
        
        // 优化状态
        private bool m_IsOptimizationActive;
        private int m_OptimizationLevel; // 0-3, 0=无优化, 3=最大优化
        #endregion

        #region Properties
        public bool IsOptimizationActive => m_IsOptimizationActive;
        public int OptimizationLevel => m_OptimizationLevel;
        public float CurrentCPUUsage => m_CurrentCPUUsage;
        public float CurrentGPUUsage => m_CurrentGPUUsage;
        public float DeviceTemperature => m_DeviceTemperature;
        #endregion

        #region Unity Lifecycle
        private void Start()
        {
            if (IsMobileDevice())
            {
                InitializeMobileOptimizations();
            }
            else
            {
                Debug.Log("[MobilePerformanceOptimizer] 非移动设备，跳过移动优化");
                enabled = false;
            }
        }

        private void Update()
        {
            if (!m_IsInitialized) return;

            MonitorDevicePerformance();
            CheckThermalStatus();
            CheckBatteryStatus();
            AdjustOptimizationLevel();
        }
        #endregion

        #region Initialization
        private void InitializeMobileOptimizations()
        {
            Debug.Log("[MobilePerformanceOptimizer] 初始化移动设备优化");

            // 保存原始设置
            m_OriginalTargetFrameRate = Application.targetFrameRate;
            
            // 启用多线程渲染
            if (m_EnableMultithreadedRendering)
            {
                EnableMultithreadedRendering();
            }

            // 优化内存设置
            OptimizeMemorySettings();

            // 优化渲染设置
            OptimizeRenderingForMobile();

            // 优化物理设置
            OptimizePhysicsForMobile();

            // 启用GPU优化
            if (m_EnableGPUOptimization)
            {
                EnableGPUOptimizations();
            }

            m_IsInitialized = true;
            Debug.Log("[MobilePerformanceOptimizer] 移动设备优化初始化完成");
        }

        private bool IsMobileDevice()
        {
            return Application.platform == RuntimePlatform.Android || 
                   Application.platform == RuntimePlatform.IPhonePlayer;
        }
        #endregion

        #region CPU Optimizations
        private void EnableMultithreadedRendering()
        {
            // Unity会自动处理多线程渲染，但我们可以优化相关设置
            Debug.Log("[MobilePerformanceOptimizer] 启用多线程渲染优化");

            // 优化异步上传设置
            QualitySettings.asyncUploadTimeSlice = 4; // 增加时间片
            QualitySettings.asyncUploadBufferSize = 32; // 增加缓冲区大小
            QualitySettings.asyncUploadPersistentBuffer = true;

            // 设置合理的工作线程数
            Application.backgroundLoadingPriority = ThreadPriority.Normal;
        }

        private void OptimizeCPUUsage()
        {
            if (m_CurrentCPUUsage > m_TargetCPUUsage)
            {
                // CPU使用率过高，需要优化
                Debug.Log($"[MobilePerformanceOptimizer] CPU使用率过高 ({m_CurrentCPUUsage:P0})，应用优化");

                // 降低物理更新频率
                if (Time.fixedDeltaTime < 1.0f / 30.0f)
                {
                    Time.fixedDeltaTime = 1.0f / 30.0f;
                }

                // 减少粒子系统预算
                QualitySettings.particleRaycastBudget = Mathf.Max(4, QualitySettings.particleRaycastBudget / 2);

                // 降低LOD偏差
                QualitySettings.lodBias = Mathf.Max(0.5f, QualitySettings.lodBias * 0.8f);
            }
        }
        #endregion

        #region GPU Optimizations
        private void EnableGPUOptimizations()
        {
            Debug.Log("[MobilePerformanceOptimizer] 启用GPU优化");

            // 优化渲染管线
            var renderPipelineAsset = GraphicsSettings.currentRenderPipeline;
            if (renderPipelineAsset != null)
            {
                // 这些设置需要在渲染管线Asset中配置，这里主要是运行时优化
                OptimizeRenderPipelineSettings();
            }

            // 优化纹理设置
            OptimizeTextureSettings();

            // 优化着色器设置
            OptimizeShaderSettings();
        }

        private void OptimizeRenderPipelineSettings()
        {
            Debug.Log("[MobilePerformanceOptimizer] 优化渲染管线设置");

            // 动态调整渲染分辨率
            if (m_EnableDynamicResolution)
            {
                AdjustRenderScale();
            }
        }

        private void AdjustRenderScale()
        {
            var renderPipelineAsset = GraphicsSettings.currentRenderPipeline;
            if (renderPipelineAsset == null) return;

            float targetScale = 1.0f;

            // 根据GPU使用率调整渲染分辨率
            if (m_CurrentGPUUsage > m_TargetGPUUsage)
            {
                targetScale = Mathf.Max(0.7f, 1.0f - (m_CurrentGPUUsage - m_TargetGPUUsage) * 2.0f);
            }

            // 根据优化等级调整
            switch (m_OptimizationLevel)
            {
                case 1: targetScale *= 0.95f; break;
                case 2: targetScale *= 0.85f; break;
                case 3: targetScale *= 0.75f; break;
            }

            // 尝试通过反射设置渲染缩放
            try
            {
                var renderScaleProperty = renderPipelineAsset.GetType().GetProperty("renderScale");
                if (renderScaleProperty != null)
                {
                    renderScaleProperty.SetValue(renderPipelineAsset, targetScale);

                    if (Mathf.Abs(targetScale - m_OriginalRenderScale) > 0.05f)
                    {
                        Debug.Log($"[MobilePerformanceOptimizer] 调整渲染分辨率至 {targetScale:F2}");
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogWarning($"[MobilePerformanceOptimizer] 无法设置渲染缩放: {e.Message}");
            }
        }

        private void OptimizeTextureSettings()
        {
            // 移动设备纹理优化
            QualitySettings.globalTextureMipmapLimit = 1; // 降低纹理质量
            QualitySettings.anisotropicFiltering = AnisotropicFiltering.Disable; // 禁用各向异性过滤
            
            Debug.Log("[MobilePerformanceOptimizer] 已优化纹理设置");
        }

        private void OptimizeShaderSettings()
        {
            // 着色器优化设置
            Shader.globalRenderPipeline = "UniversalPipeline";
            
            // 设置着色器LOD
            Shader.globalMaximumLOD = 200; // 限制着色器复杂度
            
            Debug.Log("[MobilePerformanceOptimizer] 已优化着色器设置");
        }
        #endregion

        #region Memory Optimizations
        private void OptimizeMemorySettings()
        {
            Debug.Log("[MobilePerformanceOptimizer] 优化内存设置");

            // 启用纹理流送
            QualitySettings.streamingMipmapsActive = true;
            QualitySettings.streamingMipmapsMemoryBudget = 256; // 256MB纹理内存预算
            QualitySettings.streamingMipmapsMaxLevelReduction = 2;

            // 优化网格设置
            QualitySettings.skinWeights = SkinWeights.TwoBones; // 限制骨骼权重

            // 强制垃圾回收
            System.GC.Collect();
        }
        #endregion

        #region Physics Optimizations
        private void OptimizePhysicsForMobile()
        {
            Debug.Log("[MobilePerformanceOptimizer] 优化物理设置");

            // 降低物理更新频率
            Time.fixedDeltaTime = 1.0f / 50.0f; // 50Hz物理更新

            // 优化物理求解器
            Physics.defaultSolverIterations = 4;
            Physics.defaultSolverVelocityIterations = 1;

            // 设置合理的睡眠阈值
            Physics.sleepThreshold = 0.005f;

            // 限制碰撞检测
            Physics.defaultContactOffset = 0.01f;
        }
        #endregion

        #region Performance Monitoring
        private void MonitorDevicePerformance()
        {
            // 模拟性能监控（实际项目中可能需要使用原生插件）
            m_CurrentCPUUsage = GetCPUUsage();
            m_CurrentGPUUsage = GetGPUUsage();
            m_DeviceTemperature = GetDeviceTemperature();
        }

        private float GetCPUUsage()
        {
            // 简化的CPU使用率估算
            // 实际项目中应该使用原生插件获取真实数据
            float baseUsage = 0.3f;
            float frameTimeUsage = Mathf.Clamp01(Time.deltaTime * 60.0f / 2.0f); // 基于帧时间估算
            return Mathf.Clamp01(baseUsage + frameTimeUsage * 0.5f);
        }

        private float GetGPUUsage()
        {
            // 简化的GPU使用率估算
            // 基于渲染复杂度和帧率估算
            float baseUsage = 0.4f;
            float frameRateUsage = Mathf.Clamp01((120.0f - (1.0f / Time.deltaTime)) / 60.0f);
            return Mathf.Clamp01(baseUsage + frameRateUsage * 0.4f);
        }

        private float GetDeviceTemperature()
        {
            // 模拟设备温度
            // 实际项目中需要使用原生插件
            return 35.0f + m_CurrentCPUUsage * 10.0f + m_CurrentGPUUsage * 8.0f;
        }
        #endregion

        #region Thermal Management
        private void CheckThermalStatus()
        {
            if (!m_EnableThermalManagement) return;
            if (Time.time - m_LastThermalCheck < c_ThermalCheckInterval) return;

            m_LastThermalCheck = Time.time;

            bool wasThermalThrottling = m_IsThermalThrottling;
            m_IsThermalThrottling = m_DeviceTemperature > m_ThermalThreshold;

            if (m_IsThermalThrottling && !wasThermalThrottling)
            {
                Debug.LogWarning($"[MobilePerformanceOptimizer] 设备温度过高 ({m_DeviceTemperature:F1}°C)，启用热保护");
                ApplyThermalThrottling();
            }
            else if (!m_IsThermalThrottling && wasThermalThrottling)
            {
                Debug.Log("[MobilePerformanceOptimizer] 设备温度正常，解除热保护");
                RemoveThermalThrottling();
            }
        }

        private void ApplyThermalThrottling()
        {
            // 降低目标帧率
            Application.targetFrameRate = 60;

            // 进一步降低渲染质量
            var renderPipelineAsset = GraphicsSettings.currentRenderPipeline;
            if (renderPipelineAsset != null)
            {
                try
                {
                    var renderScaleProperty = renderPipelineAsset.GetType().GetProperty("renderScale");
                    if (renderScaleProperty != null)
                    {
                        renderScaleProperty.SetValue(renderPipelineAsset, 0.7f);
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogWarning($"[MobilePerformanceOptimizer] 热保护时无法设置渲染缩放: {e.Message}");
                }
            }

            // 降低物理更新频率
            Time.fixedDeltaTime = 1.0f / 30.0f;

            m_OptimizationLevel = Mathf.Max(m_OptimizationLevel, 2);
        }

        private void RemoveThermalThrottling()
        {
            // 恢复正常帧率
            Application.targetFrameRate = m_OriginalTargetFrameRate;

            // 恢复渲染质量
            var renderPipelineAsset = GraphicsSettings.currentRenderPipeline;
            if (renderPipelineAsset != null)
            {
                try
                {
                    var renderScaleProperty = renderPipelineAsset.GetType().GetProperty("renderScale");
                    if (renderScaleProperty != null)
                    {
                        renderScaleProperty.SetValue(renderPipelineAsset, m_OriginalRenderScale);
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogWarning($"[MobilePerformanceOptimizer] 恢复热保护时无法设置渲染缩放: {e.Message}");
                }
            }

            // 恢复物理更新频率
            Time.fixedDeltaTime = 1.0f / 60.0f;
        }
        #endregion

        #region Battery Management
        private void CheckBatteryStatus()
        {
            if (!m_EnableBatteryAwareOptimization) return;
            if (Time.time - m_LastBatteryCheck < c_BatteryCheckInterval) return;

            m_LastBatteryCheck = Time.time;
            m_BatteryLevel = SystemInfo.batteryLevel;

            bool wasLowBattery = m_IsLowBattery;
            m_IsLowBattery = m_BatteryLevel > 0 && m_BatteryLevel < m_LowBatteryThreshold;

            if (m_IsLowBattery && !wasLowBattery)
            {
                Debug.LogWarning($"[MobilePerformanceOptimizer] 电量不足 ({m_BatteryLevel:P0})，启用省电模式");
                ApplyBatterySavingMode();
            }
            else if (!m_IsLowBattery && wasLowBattery)
            {
                Debug.Log("[MobilePerformanceOptimizer] 电量充足，解除省电模式");
                RemoveBatterySavingMode();
            }
        }

        private void ApplyBatterySavingMode()
        {
            // 降低目标帧率以节省电量
            Application.targetFrameRate = 60;

            // 进一步优化设置
            m_OptimizationLevel = Mathf.Max(m_OptimizationLevel, 1);
        }

        private void RemoveBatterySavingMode()
        {
            // 恢复正常帧率
            Application.targetFrameRate = m_OriginalTargetFrameRate;
        }
        #endregion

        #region Optimization Level Management
        private void AdjustOptimizationLevel()
        {
            int newLevel = 0;

            // 根据各种因素确定优化等级
            if (m_IsThermalThrottling) newLevel = Mathf.Max(newLevel, 2);
            if (m_IsLowBattery) newLevel = Mathf.Max(newLevel, 1);
            if (m_CurrentCPUUsage > m_TargetCPUUsage) newLevel = Mathf.Max(newLevel, 1);
            if (m_CurrentGPUUsage > m_TargetGPUUsage) newLevel = Mathf.Max(newLevel, 1);

            if (newLevel != m_OptimizationLevel)
            {
                m_OptimizationLevel = newLevel;
                ApplyOptimizationLevel(newLevel);
            }

            m_IsOptimizationActive = newLevel > 0;
        }

        private void ApplyOptimizationLevel(int level)
        {
            Debug.Log($"[MobilePerformanceOptimizer] 应用优化等级 {level}");

            switch (level)
            {
                case 0: // 无优化
                    RestoreOriginalSettings();
                    break;
                case 1: // 轻度优化
                    ApplyLightOptimization();
                    break;
                case 2: // 中度优化
                    ApplyMediumOptimization();
                    break;
                case 3: // 重度优化
                    ApplyHeavyOptimization();
                    break;
            }
        }

        private void RestoreOriginalSettings()
        {
            Application.targetFrameRate = m_OriginalTargetFrameRate;
            Time.fixedDeltaTime = 1.0f / 60.0f;

            var renderPipelineAsset = GraphicsSettings.currentRenderPipeline;
            if (renderPipelineAsset != null)
            {
                try
                {
                    var renderScaleProperty = renderPipelineAsset.GetType().GetProperty("renderScale");
                    if (renderScaleProperty != null)
                    {
                        renderScaleProperty.SetValue(renderPipelineAsset, m_OriginalRenderScale);
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogWarning($"[MobilePerformanceOptimizer] 恢复原始设置时无法设置渲染缩放: {e.Message}");
                }
            }
        }

        private void ApplyLightOptimization()
        {
            QualitySettings.lodBias = 0.8f;
            QualitySettings.particleRaycastBudget = 32;
        }

        private void ApplyMediumOptimization()
        {
            ApplyLightOptimization();
            Time.fixedDeltaTime = 1.0f / 45.0f;
            QualitySettings.lodBias = 0.6f;
            QualitySettings.particleRaycastBudget = 16;
        }

        private void ApplyHeavyOptimization()
        {
            ApplyMediumOptimization();
            Application.targetFrameRate = 60;
            Time.fixedDeltaTime = 1.0f / 30.0f;
            QualitySettings.lodBias = 0.4f;
            QualitySettings.particleRaycastBudget = 8;
            
            var renderPipelineAsset = GraphicsSettings.currentRenderPipeline;
            if (renderPipelineAsset != null)
            {
                try
                {
                    var renderScaleProperty = renderPipelineAsset.GetType().GetProperty("renderScale");
                    if (renderScaleProperty != null)
                    {
                        renderScaleProperty.SetValue(renderPipelineAsset, 0.75f);
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogWarning($"[MobilePerformanceOptimizer] 重度优化时无法设置渲染缩放: {e.Message}");
                }
            }
        }
        #endregion

        #region Public API
        /// <summary>
        /// 手动设置优化等级
        /// </summary>
        public void SetOptimizationLevel(int level)
        {
            m_OptimizationLevel = Mathf.Clamp(level, 0, 3);
            ApplyOptimizationLevel(m_OptimizationLevel);
        }

        /// <summary>
        /// 获取性能报告
        /// </summary>
        public string GetPerformanceReport()
        {
            return $"CPU: {m_CurrentCPUUsage:P0} | GPU: {m_CurrentGPUUsage:P0} | " +
                   $"温度: {m_DeviceTemperature:F1}°C | 电量: {m_BatteryLevel:P0} | " +
                   $"优化等级: {m_OptimizationLevel}";
        }
        #endregion
    }
}
