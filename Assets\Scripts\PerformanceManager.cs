using UnityEngine;
using UnityEngine.Rendering;
using System.Collections;

namespace Performance
{
    /// <summary>
    /// 性能管理器 - 负责优化游戏性能以达到120FPS目标
    /// 包含帧率控制、质量设置、移动设备优化等功能
    /// </summary>
    public class PerformanceManager : MonoBehaviour
    {
        #region Constants
        private const int c_TargetFrameRate = 120;
        private const int c_FallbackFrameRate = 60;
        private const float c_PerformanceCheckInterval = 2.0f;
        private const float c_MinAcceptableFPS = 100.0f;
        #endregion

        #region Serialized Fields
        [Header("性能设置")]
        [SerializeField, Tooltip("启用120FPS模式")]
        private bool m_Enable120FPS = true;

        [SerializeField, Tooltip("启用自适应质量调整")]
        private bool m_EnableAdaptiveQuality = true;

        [SerializeField, Tooltip("启用性能监控")]
        private bool m_EnablePerformanceMonitoring = true;

        [Header("移动设备优化")]
        [SerializeField, Tooltip("启用移动设备特定优化")]
        private bool m_EnableMobileOptimizations = true;

        [SerializeField, Tooltip("启用电池优化模式")]
        private bool m_EnableBatteryOptimization = false;

        [Header("渲染优化")]
        [SerializeField, Tooltip("目标渲染分辨率缩放")]
        [Range(0.5f, 1.0f)]
        private float m_RenderScale = 1.0f;

        [SerializeField, Tooltip("阴影质量等级")]
        [Range(0, 3)]
        private int m_ShadowQuality = 2;

        [Header("调试信息")]
        [SerializeField, Tooltip("显示FPS计数器")]
        private bool m_ShowFPSCounter = true;

        [SerializeField, Tooltip("显示性能统计")]
        private bool m_ShowPerformanceStats = false;
        #endregion

        #region Private Fields
        private static PerformanceManager s_Instance;
        private RenderPipelineAsset m_RenderPipelineAsset;
        private int m_CurrentQualityLevel;
        private float m_LastPerformanceCheck;
        private int m_FrameCount;
        private float m_FPSTimer;
        private float m_CurrentFPS;
        private bool m_IsInitialized;

        // 性能统计
        private float m_AverageFPS;
        private float m_MinFPS = float.MaxValue;
        private float m_MaxFPS;
        private int m_QualityAdjustments;
        #endregion

        #region Properties
        public static PerformanceManager Instance => s_Instance;
        public float CurrentFPS => m_CurrentFPS;
        public bool Is120FPSEnabled => m_Enable120FPS;
        public float RenderScale => m_RenderScale;
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            InitializeSingleton();
            InitializePerformanceSettings();
        }

        private void Start()
        {
            StartCoroutine(InitializeWithDelay());
        }

        private void Update()
        {
            UpdateFPSCounter();
            
            if (m_EnablePerformanceMonitoring)
            {
                MonitorPerformance();
            }
        }

        private void OnGUI()
        {
            if (m_ShowFPSCounter || m_ShowPerformanceStats)
            {
                DrawPerformanceUI();
            }
        }

        private void OnDestroy()
        {
            if (s_Instance == this)
            {
                s_Instance = null;
            }
        }
        #endregion

        #region Initialization
        private void InitializeSingleton()
        {
            if (s_Instance == null)
            {
                s_Instance = this;
                DontDestroyOnLoad(gameObject);
                Debug.Log("[PerformanceManager] 性能管理器已初始化");
            }
            else if (s_Instance != this)
            {
                Debug.LogWarning("[PerformanceManager] 检测到重复的性能管理器实例，销毁当前实例");
                Destroy(gameObject);
                return;
            }
        }

        private void InitializePerformanceSettings()
        {
            // 设置目标帧率
            SetTargetFrameRate();
            
            // 获取渲染管线资产
            m_RenderPipelineAsset = GraphicsSettings.currentRenderPipeline;
            
            // 记录当前质量等级
            m_CurrentQualityLevel = QualitySettings.GetQualityLevel();
            
            // 应用移动设备优化
            if (m_EnableMobileOptimizations && IsMobileDevice())
            {
                ApplyMobileOptimizations();
            }
            
            m_IsInitialized = true;
            Debug.Log($"[PerformanceManager] 性能设置已初始化 - 目标帧率: {Application.targetFrameRate}");
        }

        private IEnumerator InitializeWithDelay()
        {
            yield return new WaitForSeconds(0.5f);
            
            // 延迟应用一些设置以确保所有系统都已初始化
            OptimizeRenderingSettings();
            OptimizePhysicsSettings();
            
            Debug.Log("[PerformanceManager] 延迟初始化完成");
        }
        #endregion

        #region Frame Rate Management
        private void SetTargetFrameRate()
        {
            if (m_Enable120FPS)
            {
                Application.targetFrameRate = c_TargetFrameRate;
                
                // 禁用垂直同步以允许高帧率
                QualitySettings.vSyncCount = 0;
                
                Debug.Log($"[PerformanceManager] 已设置目标帧率为 {c_TargetFrameRate} FPS");
            }
            else
            {
                Application.targetFrameRate = c_FallbackFrameRate;
                QualitySettings.vSyncCount = 1;
                
                Debug.Log($"[PerformanceManager] 已设置目标帧率为 {c_FallbackFrameRate} FPS (垂直同步开启)");
            }
        }

        public void Toggle120FPS(bool enable)
        {
            m_Enable120FPS = enable;
            SetTargetFrameRate();
            
            Debug.Log($"[PerformanceManager] 120FPS模式已{(enable ? "启用" : "禁用")}");
        }
        #endregion

        #region Performance Monitoring
        private void UpdateFPSCounter()
        {
            m_FrameCount++;
            m_FPSTimer += Time.unscaledDeltaTime;
            
            if (m_FPSTimer >= 1.0f)
            {
                m_CurrentFPS = m_FrameCount / m_FPSTimer;
                
                // 更新统计信息
                if (m_CurrentFPS < m_MinFPS) m_MinFPS = m_CurrentFPS;
                if (m_CurrentFPS > m_MaxFPS) m_MaxFPS = m_CurrentFPS;
                
                m_FrameCount = 0;
                m_FPSTimer = 0.0f;
            }
        }

        private void MonitorPerformance()
        {
            if (Time.time - m_LastPerformanceCheck < c_PerformanceCheckInterval)
                return;
                
            m_LastPerformanceCheck = Time.time;
            
            if (m_EnableAdaptiveQuality && m_Enable120FPS)
            {
                // 如果FPS低于阈值，自动降低质量
                if (m_CurrentFPS < c_MinAcceptableFPS)
                {
                    AdjustQualityDown();
                }
                // 如果FPS稳定高于目标，可以尝试提升质量
                else if (m_CurrentFPS > c_TargetFrameRate + 10)
                {
                    AdjustQualityUp();
                }
            }
        }
        #endregion

        #region Quality Management
        private void AdjustQualityDown()
        {
            if (m_CurrentQualityLevel > 0)
            {
                m_CurrentQualityLevel--;
                QualitySettings.SetQualityLevel(m_CurrentQualityLevel, true);
                m_QualityAdjustments++;
                
                Debug.Log($"[PerformanceManager] 性能不足，降低质量等级至 {m_CurrentQualityLevel}");
            }
            else
            {
                // 如果已经是最低质量，进一步优化渲染设置
                if (m_RenderScale > 0.7f)
                {
                    m_RenderScale = Mathf.Max(0.7f, m_RenderScale - 0.1f);
                    ApplyRenderScale();
                    Debug.Log($"[PerformanceManager] 降低渲染分辨率至 {m_RenderScale:F1}");
                }
            }
        }

        private void AdjustQualityUp()
        {
            // 谨慎提升质量，避免频繁调整
            if (m_QualityAdjustments > 0 && m_CurrentQualityLevel < QualitySettings.names.Length - 1)
            {
                m_CurrentQualityLevel++;
                QualitySettings.SetQualityLevel(m_CurrentQualityLevel, true);
                m_QualityAdjustments--;
                
                Debug.Log($"[PerformanceManager] 性能充足，提升质量等级至 {m_CurrentQualityLevel}");
            }
        }
        #endregion

        #region Mobile Optimizations
        private bool IsMobileDevice()
        {
            return Application.platform == RuntimePlatform.Android || 
                   Application.platform == RuntimePlatform.IPhonePlayer;
        }

        private void ApplyMobileOptimizations()
        {
            Debug.Log("[PerformanceManager] 应用移动设备优化设置");
            
            // 降低阴影质量
            QualitySettings.shadows = ShadowQuality.HardOnly;
            QualitySettings.shadowResolution = ShadowResolution.Low;
            QualitySettings.shadowDistance = 30f;
            
            // 优化纹理质量
            QualitySettings.globalTextureMipmapLimit = 1;
            QualitySettings.anisotropicFiltering = AnisotropicFiltering.Disable;
            
            // 禁用一些高级特性
            QualitySettings.realtimeReflectionProbes = false;
            QualitySettings.billboardsFaceCameraPosition = false;
            
            // 优化粒子系统
            QualitySettings.particleRaycastBudget = 16;
            
            // 设置合理的LOD偏差
            QualitySettings.lodBias = 0.7f;
            
            // 电池优化
            if (m_EnableBatteryOptimization)
            {
                ApplyBatteryOptimizations();
            }
        }

        private void ApplyBatteryOptimizations()
        {
            Debug.Log("[PerformanceManager] 应用电池优化设置");
            
            // 进一步降低渲染质量以节省电量
            m_RenderScale = 0.8f;
            ApplyRenderScale();
            
            // 降低物理更新频率
            Time.fixedDeltaTime = 1.0f / 30.0f; // 30Hz物理更新
        }
        #endregion

        #region Rendering Optimizations
        private void OptimizeRenderingSettings()
        {
            Debug.Log("[PerformanceManager] 优化渲染设置");

            // 应用渲染分辨率缩放
            ApplyRenderScale();

            // 优化阴影设置
            OptimizeShadowSettings();
        }

        private void ApplyRenderScale()
        {
            // 尝试通过反射设置渲染缩放，如果URP可用的话
            try
            {
                if (m_RenderPipelineAsset != null)
                {
                    var renderScaleProperty = m_RenderPipelineAsset.GetType().GetProperty("renderScale");
                    if (renderScaleProperty != null)
                    {
                        renderScaleProperty.SetValue(m_RenderPipelineAsset, m_RenderScale);
                        Debug.Log($"[PerformanceManager] 渲染分辨率缩放设置为 {m_RenderScale:F2}");
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogWarning($"[PerformanceManager] 无法设置渲染缩放: {e.Message}");
            }
        }

        private void OptimizeShadowSettings()
        {
            // 根据质量等级调整阴影设置
            switch (m_ShadowQuality)
            {
                case 0:
                    QualitySettings.shadows = ShadowQuality.Disable;
                    break;
                case 1:
                    QualitySettings.shadows = ShadowQuality.HardOnly;
                    QualitySettings.shadowResolution = ShadowResolution.Low;
                    break;
                case 2:
                    QualitySettings.shadows = ShadowQuality.All;
                    QualitySettings.shadowResolution = ShadowResolution.Medium;
                    break;
                case 3:
                    QualitySettings.shadows = ShadowQuality.All;
                    QualitySettings.shadowResolution = ShadowResolution.High;
                    break;
            }
        }

        private void OptimizePhysicsSettings()
        {
            // 为高帧率优化物理设置
            if (m_Enable120FPS)
            {
                // 保持物理更新在合理范围内
                Time.fixedDeltaTime = 1.0f / 60.0f; // 60Hz物理更新足够大多数游戏
                Physics.defaultSolverIterations = 4; // 降低求解器迭代次数
                Physics.defaultSolverVelocityIterations = 1;
                
                Debug.Log("[PerformanceManager] 已优化物理设置以支持高帧率");
            }
        }
        #endregion

        #region UI and Debug
        private void DrawPerformanceUI()
        {
            GUIStyle style = new GUIStyle(GUI.skin.label);
            style.fontSize = 24;
            style.normal.textColor = Color.white;
            style.fontStyle = FontStyle.Bold;
            
            float yOffset = 10f;
            
            if (m_ShowFPSCounter)
            {
                // FPS显示
                Color fpsColor = GetFPSColor(m_CurrentFPS);
                style.normal.textColor = fpsColor;
                
                GUI.Label(new Rect(10, yOffset, 200, 30), 
                    $"FPS: {m_CurrentFPS:F0}", style);
                yOffset += 35f;
                
                // 目标FPS指示
                style.normal.textColor = Color.yellow;
                GUI.Label(new Rect(10, yOffset, 200, 25), 
                    $"目标: {Application.targetFrameRate}", style);
                yOffset += 30f;
            }
            
            if (m_ShowPerformanceStats)
            {
                style.fontSize = 18;
                style.normal.textColor = Color.cyan;
                
                GUI.Label(new Rect(10, yOffset, 300, 25), 
                    $"最小FPS: {m_MinFPS:F0} | 最大FPS: {m_MaxFPS:F0}", style);
                yOffset += 25f;
                
                GUI.Label(new Rect(10, yOffset, 300, 25), 
                    $"质量等级: {QualitySettings.names[m_CurrentQualityLevel]}", style);
                yOffset += 25f;
                
                GUI.Label(new Rect(10, yOffset, 300, 25), 
                    $"渲染缩放: {m_RenderScale:F2}", style);
            }
        }

        private Color GetFPSColor(float fps)
        {
            if (fps >= c_TargetFrameRate * 0.9f) return Color.green;
            if (fps >= c_TargetFrameRate * 0.7f) return Color.yellow;
            return Color.red;
        }
        #endregion

        #region Public API
        /// <summary>
        /// 手动触发性能优化
        /// </summary>
        public void OptimizePerformance()
        {
            if (!m_IsInitialized) return;
            
            Debug.Log("[PerformanceManager] 手动触发性能优化");
            
            OptimizeRenderingSettings();
            OptimizePhysicsSettings();
            
            if (IsMobileDevice() && m_EnableMobileOptimizations)
            {
                ApplyMobileOptimizations();
            }
        }

        /// <summary>
        /// 重置性能统计
        /// </summary>
        public void ResetPerformanceStats()
        {
            m_MinFPS = float.MaxValue;
            m_MaxFPS = 0f;
            m_QualityAdjustments = 0;
            
            Debug.Log("[PerformanceManager] 性能统计已重置");
        }

        /// <summary>
        /// 设置渲染分辨率缩放
        /// </summary>
        public void SetRenderScale(float scale)
        {
            m_RenderScale = Mathf.Clamp(scale, 0.5f, 1.0f);
            ApplyRenderScale();
        }

        /// <summary>
        /// 切换性能监控显示
        /// </summary>
        public void TogglePerformanceDisplay(bool showFPS, bool showStats)
        {
            m_ShowFPSCounter = showFPS;
            m_ShowPerformanceStats = showStats;
        }
        #endregion
    }
}
