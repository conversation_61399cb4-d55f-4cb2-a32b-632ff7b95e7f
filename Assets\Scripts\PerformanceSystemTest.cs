using UnityEngine;
using Performance;

/// <summary>
/// 性能系统测试脚本 - 用于验证120FPS优化系统是否正常工作
/// </summary>
public class PerformanceSystemTest : MonoBehaviour
{
    #region Serialized Fields
    [Header("测试设置")]
    [SerializeField, Tooltip("自动运行测试")]
    private bool m_AutoRunTest = true;

    [SerializeField, Tooltip("测试持续时间（秒）")]
    private float m_TestDuration = 10.0f;

    [SerializeField, Tooltip("显示详细日志")]
    private bool m_ShowDetailedLogs = true;
    #endregion

    #region Private Fields
    private PerformanceManager m_PerformanceManager;
    private MobilePerformanceOptimizer m_MobileOptimizer;
    private bool m_TestCompleted = false;
    private float m_TestStartTime;
    #endregion

    #region Unity Lifecycle
    private void Start()
    {
        if (m_AutoRunTest)
        {
            StartTest();
        }
    }

    private void Update()
    {
        if (m_AutoRunTest && !m_TestCompleted)
        {
            CheckTestProgress();
        }
    }
    #endregion

    #region Test Methods
    public void StartTest()
    {
        Debug.Log("=== 开始性能系统测试 ===");
        
        m_TestStartTime = Time.time;
        m_TestCompleted = false;

        // 查找性能管理器组件
        FindPerformanceComponents();

        // 测试基本功能
        TestBasicFunctionality();

        // 测试120FPS设置
        Test120FPSSettings();

        // 测试移动设备优化（如果适用）
        if (IsMobileDevice())
        {
            TestMobileOptimizations();
        }

        Debug.Log("=== 性能系统测试启动完成 ===");
    }

    private void FindPerformanceComponents()
    {
        m_PerformanceManager = FindObjectOfType<PerformanceManager>();
        m_MobileOptimizer = FindObjectOfType<MobilePerformanceOptimizer>();

        if (m_ShowDetailedLogs)
        {
            Debug.Log($"[测试] PerformanceManager: {(m_PerformanceManager != null ? "找到" : "未找到")}");
            Debug.Log($"[测试] MobilePerformanceOptimizer: {(m_MobileOptimizer != null ? "找到" : "未找到")}");
        }
    }

    private void TestBasicFunctionality()
    {
        Debug.Log("--- 测试基本功能 ---");

        // 测试帧率设置
        int targetFrameRate = Application.targetFrameRate;
        int vSyncCount = QualitySettings.vSyncCount;

        Debug.Log($"[测试] 目标帧率: {targetFrameRate}");
        Debug.Log($"[测试] 垂直同步: {(vSyncCount > 0 ? "开启" : "关闭")}");

        // 测试质量设置
        string qualityLevel = QualitySettings.names[QualitySettings.GetQualityLevel()];
        Debug.Log($"[测试] 质量等级: {qualityLevel}");

        // 测试物理设置
        float physicsFrequency = 1.0f / Time.fixedDeltaTime;
        Debug.Log($"[测试] 物理更新频率: {physicsFrequency:F0}Hz");
    }

    private void Test120FPSSettings()
    {
        Debug.Log("--- 测试120FPS设置 ---");

        bool is120FPSTarget = Application.targetFrameRate == 120;
        bool isVSyncDisabled = QualitySettings.vSyncCount == 0;

        Debug.Log($"[测试] 120FPS目标: {(is120FPSTarget ? "✓ 正确" : "✗ 错误")}");
        Debug.Log($"[测试] 垂直同步禁用: {(isVSyncDisabled ? "✓ 正确" : "✗ 错误")}");

        if (m_PerformanceManager != null)
        {
            Debug.Log($"[测试] PerformanceManager 120FPS模式: {(m_PerformanceManager.Is120FPSEnabled ? "✓ 启用" : "✗ 禁用")}");
            Debug.Log($"[测试] 当前FPS: {m_PerformanceManager.CurrentFPS:F1}");
        }
    }

    private void TestMobileOptimizations()
    {
        Debug.Log("--- 测试移动设备优化 ---");

        if (m_MobileOptimizer != null)
        {
            Debug.Log($"[测试] 移动优化器状态: ✓ 运行中");
            Debug.Log($"[测试] 优化等级: {m_MobileOptimizer.OptimizationLevel}");
            Debug.Log($"[测试] CPU使用率: {m_MobileOptimizer.CurrentCPUUsage:P0}");
            Debug.Log($"[测试] GPU使用率: {m_MobileOptimizer.CurrentGPUUsage:P0}");
            Debug.Log($"[测试] 设备温度: {m_MobileOptimizer.DeviceTemperature:F1}°C");
        }
        else
        {
            Debug.LogWarning("[测试] 移动优化器未找到");
        }

        // 测试移动设备特定设置
        Debug.Log($"[测试] 纹理Mipmap限制: {QualitySettings.globalTextureMipmapLimit}");
        Debug.Log($"[测试] 各向异性过滤: {QualitySettings.anisotropicFiltering}");
        Debug.Log($"[测试] 实时反射探针: {QualitySettings.realtimeReflectionProbes}");
        Debug.Log($"[测试] 粒子光线投射预算: {QualitySettings.particleRaycastBudget}");
    }

    private void CheckTestProgress()
    {
        float elapsedTime = Time.time - m_TestStartTime;
        
        if (elapsedTime >= m_TestDuration)
        {
            CompleteTest();
        }
        else if (m_ShowDetailedLogs && Mathf.FloorToInt(elapsedTime) % 2 == 0)
        {
            // 每2秒输出一次性能数据
            LogPerformanceData();
        }
    }

    private void LogPerformanceData()
    {
        float currentFPS = 1.0f / Time.deltaTime;
        
        if (m_ShowDetailedLogs)
        {
            Debug.Log($"[性能] 当前FPS: {currentFPS:F1} | 目标: {Application.targetFrameRate}");
            
            if (m_MobileOptimizer != null)
            {
                Debug.Log($"[性能] {m_MobileOptimizer.GetPerformanceReport()}");
            }
        }
    }

    private void CompleteTest()
    {
        m_TestCompleted = true;
        
        Debug.Log("=== 性能系统测试完成 ===");
        
        // 生成测试报告
        GenerateTestReport();
    }

    private void GenerateTestReport()
    {
        Debug.Log("--- 测试报告 ---");
        
        // 基本信息
        Debug.Log($"设备型号: {SystemInfo.deviceModel}");
        Debug.Log($"操作系统: {SystemInfo.operatingSystem}");
        Debug.Log($"GPU: {SystemInfo.graphicsDeviceName}");
        Debug.Log($"内存: {SystemInfo.systemMemorySize} MB");
        Debug.Log($"显存: {SystemInfo.graphicsMemorySize} MB");
        
        // 性能设置
        Debug.Log($"目标帧率: {Application.targetFrameRate}");
        Debug.Log($"垂直同步: {(QualitySettings.vSyncCount > 0 ? "开启" : "关闭")}");
        Debug.Log($"质量等级: {QualitySettings.names[QualitySettings.GetQualityLevel()]}");
        Debug.Log($"物理频率: {1.0f/Time.fixedDeltaTime:F0}Hz");
        
        // 渲染管线信息
        var renderPipeline = GraphicsSettings.currentRenderPipeline;
        if (renderPipeline != null)
        {
            Debug.Log($"渲染管线: {renderPipeline.GetType().Name}");
            
            try
            {
                var renderScaleProperty = renderPipeline.GetType().GetProperty("renderScale");
                if (renderScaleProperty != null)
                {
                    float renderScale = (float)renderScaleProperty.GetValue(renderPipeline);
                    Debug.Log($"渲染缩放: {renderScale:F2}");
                }
            }
            catch
            {
                Debug.Log("渲染缩放: 无法获取");
            }
        }
        else
        {
            Debug.Log("渲染管线: 内置渲染管线");
        }
        
        // 组件状态
        Debug.Log($"PerformanceManager: {(m_PerformanceManager != null ? "已加载" : "未加载")}");
        Debug.Log($"MobilePerformanceOptimizer: {(m_MobileOptimizer != null ? "已加载" : "未加载")}");
        
        // 移动设备信息
        if (IsMobileDevice())
        {
            Debug.Log($"电池电量: {SystemInfo.batteryLevel:P0}");
            Debug.Log($"电池状态: {SystemInfo.batteryStatus}");
        }
        
        Debug.Log("--- 测试报告结束 ---");
    }

    private bool IsMobileDevice()
    {
        return Application.platform == RuntimePlatform.Android || 
               Application.platform == RuntimePlatform.IPhonePlayer;
    }
    #endregion

    #region Public Methods
    /// <summary>
    /// 手动启动测试
    /// </summary>
    [ContextMenu("启动性能测试")]
    public void ManualStartTest()
    {
        StartTest();
    }

    /// <summary>
    /// 切换120FPS模式测试
    /// </summary>
    [ContextMenu("测试120FPS切换")]
    public void TestToggle120FPS()
    {
        if (m_PerformanceManager != null)
        {
            bool currentState = m_PerformanceManager.Is120FPSEnabled;
            m_PerformanceManager.Toggle120FPS(!currentState);
            
            Debug.Log($"[测试] 120FPS模式切换为: {(!currentState ? "启用" : "禁用")}");
        }
        else
        {
            Debug.LogWarning("[测试] PerformanceManager未找到，无法测试120FPS切换");
        }
    }

    /// <summary>
    /// 测试移动优化等级切换
    /// </summary>
    [ContextMenu("测试移动优化等级")]
    public void TestMobileOptimizationLevels()
    {
        if (m_MobileOptimizer != null)
        {
            for (int i = 0; i <= 3; i++)
            {
                m_MobileOptimizer.SetOptimizationLevel(i);
                Debug.Log($"[测试] 移动优化等级设置为: {i}");
            }
        }
        else
        {
            Debug.LogWarning("[测试] MobilePerformanceOptimizer未找到，无法测试优化等级");
        }
    }

    /// <summary>
    /// 获取当前性能状态
    /// </summary>
    public string GetCurrentPerformanceStatus()
    {
        string status = $"FPS: {1.0f/Time.deltaTime:F1}/{Application.targetFrameRate} | ";
        status += $"质量: {QualitySettings.names[QualitySettings.GetQualityLevel()]} | ";
        status += $"VSync: {(QualitySettings.vSyncCount > 0 ? "开" : "关")}";
        
        if (m_MobileOptimizer != null)
        {
            status += $" | 移动优化: {m_MobileOptimizer.OptimizationLevel}";
        }
        
        return status;
    }
    #endregion
}
