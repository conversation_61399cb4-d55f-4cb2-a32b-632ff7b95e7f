using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;

/// <summary>
/// 性能测试场景 - 用于验证120FPS优化效果
/// </summary>
public class PerformanceTestScene : MonoBehaviour
{
    #region Serialized Fields
    [Header("测试对象")]
    [SerializeField, Tooltip("用于性能测试的移动对象")]
    private Transform[] m_TestObjects;

    [SerializeField, Tooltip("测试对象移动速度")]
    private float m_MoveSpeed = 5.0f;

    [SerializeField, Tooltip("测试对象旋转速度")]
    private float m_RotateSpeed = 90.0f;

    [Header("UI组件")]
    [SerializeField, Tooltip("FPS显示文本")]
    private TextMeshProUGUI m_FPSText;

    [SerializeField, Tooltip("性能信息文本")]
    private TextMeshProUGUI m_PerformanceInfoText;

    [SerializeField, <PERSON>lt<PERSON>("测试控制按钮")]
    private Button m_StartTestButton;

    [SerializeField, Toolt<PERSON>("停止测试按钮")]
    private Button m_StopTestButton;

    [SerializeField, Toolt<PERSON>("切换120FPS按钮")]
    private Button m_Toggle120FPSButton;

    [Header("测试设置")]
    [SerializeField, Tooltip("测试持续时间（秒）")]
    private float m_TestDuration = 30.0f;

    [SerializeField, Tooltip("自动开始测试")]
    private bool m_AutoStartTest = true;
    #endregion

    #region Private Fields
    private bool m_IsTestRunning = false;
    private float m_TestStartTime;
    private int m_FrameCount;
    private float m_FPSTimer;
    private float m_CurrentFPS;
    private float m_AverageFPS;
    private float m_MinFPS = float.MaxValue;
    private float m_MaxFPS = 0f;
    private int m_TotalFrames;
    private bool m_Is120FPSEnabled = true;

    // 测试统计
    private float m_FPSSum = 0f;
    private int m_FPSSamples = 0;
    #endregion

    #region Unity Lifecycle
    private void Start()
    {
        InitializeUI();
        
        if (m_AutoStartTest)
        {
            StartCoroutine(DelayedStartTest());
        }
    }

    private void Update()
    {
        UpdateFPSCounter();
        UpdateTestObjects();
        UpdateUI();
    }
    #endregion

    #region Initialization
    private void InitializeUI()
    {
        // 设置按钮事件
        if (m_StartTestButton != null)
        {
            m_StartTestButton.onClick.AddListener(StartPerformanceTest);
        }

        if (m_StopTestButton != null)
        {
            m_StopTestButton.onClick.AddListener(StopPerformanceTest);
        }

        if (m_Toggle120FPSButton != null)
        {
            m_Toggle120FPSButton.onClick.AddListener(Toggle120FPS);
            UpdateToggleButtonText();
        }

        // 初始化文本
        if (m_FPSText != null)
        {
            m_FPSText.text = "FPS: --";
        }

        if (m_PerformanceInfoText != null)
        {
            m_PerformanceInfoText.text = "准备开始性能测试...";
        }

        Debug.Log("[PerformanceTestScene] UI已初始化");
    }

    private IEnumerator DelayedStartTest()
    {
        yield return new WaitForSeconds(1.0f);
        StartPerformanceTest();
    }
    #endregion

    #region FPS Calculation
    private void UpdateFPSCounter()
    {
        m_FrameCount++;
        m_TotalFrames++;
        m_FPSTimer += Time.unscaledDeltaTime;

        if (m_FPSTimer >= 1.0f)
        {
            m_CurrentFPS = m_FrameCount / m_FPSTimer;
            
            // 更新统计信息
            if (m_IsTestRunning)
            {
                if (m_CurrentFPS < m_MinFPS) m_MinFPS = m_CurrentFPS;
                if (m_CurrentFPS > m_MaxFPS) m_MaxFPS = m_CurrentFPS;
                
                m_FPSSum += m_CurrentFPS;
                m_FPSSamples++;
                m_AverageFPS = m_FPSSum / m_FPSSamples;
            }

            m_FrameCount = 0;
            m_FPSTimer = 0.0f;
        }
    }
    #endregion

    #region Test Objects
    private void UpdateTestObjects()
    {
        if (!m_IsTestRunning || m_TestObjects == null) return;

        float time = Time.time;
        
        for (int i = 0; i < m_TestObjects.Length; i++)
        {
            if (m_TestObjects[i] == null) continue;

            Transform obj = m_TestObjects[i];
            
            // 移动对象
            float x = Mathf.Sin(time * m_MoveSpeed + i) * 3.0f;
            float z = Mathf.Cos(time * m_MoveSpeed + i) * 3.0f;
            obj.position = new Vector3(x, obj.position.y, z);
            
            // 旋转对象
            obj.Rotate(Vector3.up, m_RotateSpeed * Time.deltaTime);
        }
    }
    #endregion

    #region UI Updates
    private void UpdateUI()
    {
        UpdateFPSDisplay();
        UpdatePerformanceInfo();
    }

    private void UpdateFPSDisplay()
    {
        if (m_FPSText == null) return;

        Color fpsColor = GetFPSColor(m_CurrentFPS);
        m_FPSText.color = fpsColor;
        m_FPSText.text = $"FPS: {m_CurrentFPS:F0} / {Application.targetFrameRate}";
    }

    private void UpdatePerformanceInfo()
    {
        if (m_PerformanceInfoText == null) return;

        string info = "";
        
        if (m_IsTestRunning)
        {
            float elapsedTime = Time.time - m_TestStartTime;
            float remainingTime = Mathf.Max(0, m_TestDuration - elapsedTime);
            
            info += $"测试进行中... 剩余时间: {remainingTime:F1}s\n";
            info += $"当前FPS: {m_CurrentFPS:F0}\n";
            info += $"平均FPS: {m_AverageFPS:F1}\n";
            info += $"最小FPS: {m_MinFPS:F0}\n";
            info += $"最大FPS: {m_MaxFPS:F0}\n";
            info += $"总帧数: {m_TotalFrames}\n";
        }
        else
        {
            info += "性能测试已停止\n";
            if (m_FPSSamples > 0)
            {
                info += $"测试结果:\n";
                info += $"平均FPS: {m_AverageFPS:F1}\n";
                info += $"最小FPS: {m_MinFPS:F0}\n";
                info += $"最大FPS: {m_MaxFPS:F0}\n";
                info += $"总帧数: {m_TotalFrames}\n";
            }
        }
        
        info += $"\n系统信息:\n";
        info += $"目标帧率: {Application.targetFrameRate}\n";
        info += $"垂直同步: {(QualitySettings.vSyncCount > 0 ? "开启" : "关闭")}\n";
        info += $"质量等级: {QualitySettings.names[QualitySettings.GetQualityLevel()]}\n";
        info += $"设备: {SystemInfo.deviceModel}\n";
        info += $"GPU: {SystemInfo.graphicsDeviceName}";

        m_PerformanceInfoText.text = info;
    }

    private Color GetFPSColor(float fps)
    {
        int targetFPS = Application.targetFrameRate;
        
        if (fps >= targetFPS * 0.9f)
            return Color.green;
        else if (fps >= targetFPS * 0.7f)
            return Color.yellow;
        else
            return Color.red;
    }
    #endregion

    #region Test Control
    public void StartPerformanceTest()
    {
        if (m_IsTestRunning) return;

        Debug.Log("[PerformanceTestScene] 开始性能测试");
        
        m_IsTestRunning = true;
        m_TestStartTime = Time.time;
        
        // 重置统计数据
        ResetStatistics();
        
        // 更新UI
        if (m_StartTestButton != null)
            m_StartTestButton.interactable = false;
        if (m_StopTestButton != null)
            m_StopTestButton.interactable = true;
        
        // 启动测试协程
        StartCoroutine(TestCoroutine());
    }

    public void StopPerformanceTest()
    {
        if (!m_IsTestRunning) return;

        Debug.Log("[PerformanceTestScene] 停止性能测试");
        
        m_IsTestRunning = false;
        
        // 更新UI
        if (m_StartTestButton != null)
            m_StartTestButton.interactable = true;
        if (m_StopTestButton != null)
            m_StopTestButton.interactable = false;
        
        // 输出测试结果
        LogTestResults();
    }

    private IEnumerator TestCoroutine()
    {
        yield return new WaitForSeconds(m_TestDuration);
        
        if (m_IsTestRunning)
        {
            StopPerformanceTest();
        }
    }

    private void ResetStatistics()
    {
        m_AverageFPS = 0f;
        m_MinFPS = float.MaxValue;
        m_MaxFPS = 0f;
        m_TotalFrames = 0;
        m_FPSSum = 0f;
        m_FPSSamples = 0;
    }

    private void LogTestResults()
    {
        Debug.Log("=== 性能测试结果 ===");
        Debug.Log($"平均FPS: {m_AverageFPS:F1}");
        Debug.Log($"最小FPS: {m_MinFPS:F0}");
        Debug.Log($"最大FPS: {m_MaxFPS:F0}");
        Debug.Log($"总帧数: {m_TotalFrames}");
        Debug.Log($"目标帧率: {Application.targetFrameRate}");
        Debug.Log($"120FPS模式: {(m_Is120FPSEnabled ? "启用" : "禁用")}");
        Debug.Log("==================");
    }
    #endregion

    #region 120FPS Toggle
    public void Toggle120FPS()
    {
        m_Is120FPSEnabled = !m_Is120FPSEnabled;
        
        if (m_Is120FPSEnabled)
        {
            Application.targetFrameRate = 120;
            QualitySettings.vSyncCount = 0;
        }
        else
        {
            Application.targetFrameRate = 60;
            QualitySettings.vSyncCount = 1;
        }
        
        UpdateToggleButtonText();
        
        Debug.Log($"[PerformanceTestScene] 120FPS模式已{(m_Is120FPSEnabled ? "启用" : "禁用")}");
    }

    private void UpdateToggleButtonText()
    {
        if (m_Toggle120FPSButton != null)
        {
            var buttonText = m_Toggle120FPSButton.GetComponentInChildren<TextMeshProUGUI>();
            if (buttonText != null)
            {
                buttonText.text = m_Is120FPSEnabled ? "禁用120FPS" : "启用120FPS";
            }
        }
    }
    #endregion

    #region Public Methods
    /// <summary>
    /// 设置测试对象数量（用于压力测试）
    /// </summary>
    public void SetTestObjectCount(int count)
    {
        // 这里可以动态创建或销毁测试对象
        Debug.Log($"[PerformanceTestScene] 设置测试对象数量: {count}");
    }

    /// <summary>
    /// 获取当前性能报告
    /// </summary>
    public string GetPerformanceReport()
    {
        return $"当前FPS: {m_CurrentFPS:F0} | 平均FPS: {m_AverageFPS:F1} | " +
               $"最小FPS: {m_MinFPS:F0} | 最大FPS: {m_MaxFPS:F0} | " +
               $"目标帧率: {Application.targetFrameRate}";
    }
    #endregion
}
