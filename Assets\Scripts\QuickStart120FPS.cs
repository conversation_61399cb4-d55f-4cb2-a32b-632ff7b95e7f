using UnityEngine;

/// <summary>
/// 快速启动120FPS - 最简单的使用方式
/// 只需要将此脚本添加到场景中的任意GameObject即可
/// </summary>
public class QuickStart120FPS : MonoBehaviour
{
    #region Serialized Fields
    [Header("快速设置")]
    [SerializeField, Toolt<PERSON>("启用120FPS")]
    private bool m_Enable120FPS = true;

    [SerializeField, Tooltip("显示FPS")]
    private bool m_ShowFPS = true;

    [SerializeField, Tooltip("移动设备优化")]
    private bool m_MobileOptimization = true;

    [SerializeField, Tooltip("显示启动日志")]
    private bool m_ShowLogs = true;
    #endregion

    #region Unity Lifecycle
    private void Start()
    {
        ApplyQuick120FPSSettings();
    }
    #endregion

    #region Quick Setup
    private void ApplyQuick120FPSSettings()
    {
        if (m_ShowLogs)
            Debug.Log("=== 快速120FPS设置开始 ===");

        // 1. 设置帧率
        if (m_Enable120FPS)
        {
            Application.targetFrameRate = 120;
            QualitySettings.vSyncCount = 0;
            if (m_ShowLogs)
                Debug.Log("✓ 120FPS已启用");
        }
        else
        {
            Application.targetFrameRate = 60;
            QualitySettings.vSyncCount = 1;
            if (m_ShowLogs)
                Debug.Log("✓ 60FPS已启用");
        }

        // 2. 优化物理
        Time.fixedDeltaTime = 1.0f / 60.0f;
        Physics.defaultSolverIterations = 4;
        Physics.defaultSolverVelocityIterations = 1;
        if (m_ShowLogs)
            Debug.Log("✓ 物理设置已优化");

        // 3. 移动设备优化
        if (m_MobileOptimization && IsMobileDevice())
        {
            ApplyMobileSettings();
        }

        // 4. 创建FPS显示
        if (m_ShowFPS)
        {
            CreateFPSDisplay();
        }

        if (m_ShowLogs)
            Debug.Log("=== 快速120FPS设置完成 ===");
    }

    private void ApplyMobileSettings()
    {
        // 移动设备基础优化
        QualitySettings.globalTextureMipmapLimit = 1;
        QualitySettings.anisotropicFiltering = AnisotropicFiltering.Disable;
        QualitySettings.realtimeReflectionProbes = false;
        QualitySettings.particleRaycastBudget = 16;
        QualitySettings.lodBias = 0.7f;
        QualitySettings.shadows = ShadowQuality.HardOnly;
        QualitySettings.shadowResolution = ShadowResolution.Low;
        QualitySettings.shadowDistance = 30f;

        if (m_ShowLogs)
            Debug.Log("✓ 移动设备优化已应用");
    }

    private void CreateFPSDisplay()
    {
        // 检查是否已存在FPS显示
        var existingFPS = FindObjectOfType<SimpleFPSDisplay>();
        if (existingFPS == null)
        {
            GameObject fpsGO = new GameObject("FPS Display");
            fpsGO.AddComponent<SimpleFPSDisplay>();
            DontDestroyOnLoad(fpsGO);
            
            if (m_ShowLogs)
                Debug.Log("✓ FPS显示已创建");
        }
    }

    private bool IsMobileDevice()
    {
        return Application.platform == RuntimePlatform.Android || 
               Application.platform == RuntimePlatform.IPhonePlayer;
    }
    #endregion

    #region Public Methods
    /// <summary>
    /// 重新应用设置
    /// </summary>
    [ContextMenu("重新应用120FPS设置")]
    public void ReapplySettings()
    {
        ApplyQuick120FPSSettings();
    }

    /// <summary>
    /// 切换120FPS
    /// </summary>
    public void Toggle120FPS()
    {
        m_Enable120FPS = !m_Enable120FPS;
        ApplyQuick120FPSSettings();
    }
    #endregion
}

/// <summary>
/// 简单的FPS显示组件
/// </summary>
public class SimpleFPSDisplay : MonoBehaviour
{
    private float m_FPSTimer;
    private int m_FrameCount;
    private float m_CurrentFPS;

    private void Update()
    {
        m_FrameCount++;
        m_FPSTimer += Time.unscaledDeltaTime;

        if (m_FPSTimer >= 1.0f)
        {
            m_CurrentFPS = m_FrameCount / m_FPSTimer;
            m_FrameCount = 0;
            m_FPSTimer = 0.0f;
        }
    }

    private void OnGUI()
    {
        GUIStyle style = new GUIStyle(GUI.skin.label);
        style.fontSize = 24;
        style.fontStyle = FontStyle.Bold;
        
        // 颜色编码
        if (m_CurrentFPS >= Application.targetFrameRate * 0.9f)
            style.normal.textColor = Color.green;
        else if (m_CurrentFPS >= Application.targetFrameRate * 0.7f)
            style.normal.textColor = Color.yellow;
        else
            style.normal.textColor = Color.red;

        // 显示FPS
        GUI.Label(new Rect(10, 10, 200, 30), $"FPS: {m_CurrentFPS:F0}", style);
        
        // 显示目标帧率
        style.fontSize = 16;
        style.normal.textColor = Color.white;
        GUI.Label(new Rect(10, 40, 200, 25), $"目标: {Application.targetFrameRate}", style);
        
        // 显示设备信息
        style.fontSize = 14;
        style.normal.textColor = Color.gray;
        GUI.Label(new Rect(10, 65, 300, 20), $"设备: {SystemInfo.deviceModel}");
        
        // 快捷键提示
        style.fontSize = 12;
        style.normal.textColor = Color.cyan;
        GUI.Label(new Rect(10, Screen.height - 40, 300, 20), "按 F1 切换120FPS | F2 隐藏FPS");
    }

    private void Update()
    {
        // 更新FPS计算
        m_FrameCount++;
        m_FPSTimer += Time.unscaledDeltaTime;

        if (m_FPSTimer >= 1.0f)
        {
            m_CurrentFPS = m_FrameCount / m_FPSTimer;
            m_FrameCount = 0;
            m_FPSTimer = 0.0f;
        }

        // 快捷键处理
        if (Input.GetKeyDown(KeyCode.F1))
        {
            ToggleFrameRate();
        }
        
        if (Input.GetKeyDown(KeyCode.F2))
        {
            gameObject.SetActive(false);
        }
    }

    private void ToggleFrameRate()
    {
        if (Application.targetFrameRate == 120)
        {
            Application.targetFrameRate = 60;
            QualitySettings.vSyncCount = 1;
            Debug.Log("切换到60FPS模式");
        }
        else
        {
            Application.targetFrameRate = 120;
            QualitySettings.vSyncCount = 0;
            Debug.Log("切换到120FPS模式");
        }
    }
}
