using UnityEngine;

/// <summary>
/// 简化版120FPS管理器 - 确保稳定工作的最小化版本
/// </summary>
public class Simple120FPSManager : MonoBehaviour
{
    #region Serialized Fields
    [Header("120FPS设置")]
    [SerializeF<PERSON>, <PERSON>lt<PERSON>("启用120FPS模式")]
    private bool m_Enable120FPS = true;

    [SerializeField, Toolt<PERSON>("显示FPS计数器")]
    private bool m_ShowFPSCounter = true;

    [SerializeField, Toolt<PERSON>("自动应用移动设备优化")]
    private bool m_AutoApplyMobileOptimizations = true;
    #endregion

    #region Private Fields
    private static Simple120FPSManager s_Instance;
    private float m_FPSTimer;
    private int m_FrameCount;
    private float m_CurrentFPS;
    private bool m_IsInitialized;
    #endregion

    #region Properties
    public static Simple120FPSManager Instance => s_Instance;
    public float CurrentFPS => m_CurrentFPS;
    public bool Is120FPSEnabled => m_Enable120FPS;
    #endregion

    #region Unity Lifecycle
    private void Awake()
    {
        InitializeSingleton();
        Initialize120FPS();
    }

    private void Update()
    {
        UpdateFPSCounter();
    }

    private void OnGUI()
    {
        if (m_ShowFPSCounter)
        {
            DrawFPSCounter();
        }
    }
    #endregion

    #region Initialization
    private void InitializeSingleton()
    {
        if (s_Instance == null)
        {
            s_Instance = this;
            DontDestroyOnLoad(gameObject);
            Debug.Log("[Simple120FPSManager] 120FPS管理器已初始化");
        }
        else if (s_Instance != this)
        {
            Debug.LogWarning("[Simple120FPSManager] 检测到重复实例，销毁当前实例");
            Destroy(gameObject);
            return;
        }
    }

    private void Initialize120FPS()
    {
        if (m_Enable120FPS)
        {
            // 设置120FPS
            Application.targetFrameRate = 120;
            QualitySettings.vSyncCount = 0;
            
            Debug.Log("[Simple120FPSManager] ✓ 120FPS模式已启用");
        }
        else
        {
            // 设置60FPS
            Application.targetFrameRate = 60;
            QualitySettings.vSyncCount = 1;
            
            Debug.Log("[Simple120FPSManager] 60FPS模式已启用");
        }

        // 优化物理设置
        OptimizePhysicsSettings();

        // 移动设备优化
        if (m_AutoApplyMobileOptimizations && IsMobileDevice())
        {
            ApplyMobileOptimizations();
        }

        m_IsInitialized = true;
    }
    #endregion

    #region FPS Counter
    private void UpdateFPSCounter()
    {
        m_FrameCount++;
        m_FPSTimer += Time.unscaledDeltaTime;

        if (m_FPSTimer >= 1.0f)
        {
            m_CurrentFPS = m_FrameCount / m_FPSTimer;
            m_FrameCount = 0;
            m_FPSTimer = 0.0f;
        }
    }

    private void DrawFPSCounter()
    {
        GUIStyle style = new GUIStyle(GUI.skin.label);
        style.fontSize = 24;
        style.fontStyle = FontStyle.Bold;
        
        // 根据FPS设置颜色
        if (m_CurrentFPS >= Application.targetFrameRate * 0.9f)
            style.normal.textColor = Color.green;
        else if (m_CurrentFPS >= Application.targetFrameRate * 0.7f)
            style.normal.textColor = Color.yellow;
        else
            style.normal.textColor = Color.red;

        GUI.Label(new Rect(10, 10, 200, 30), $"FPS: {m_CurrentFPS:F0}", style);
        
        // 显示目标帧率
        style.fontSize = 16;
        style.normal.textColor = Color.white;
        GUI.Label(new Rect(10, 40, 200, 25), $"目标: {Application.targetFrameRate}", style);
    }
    #endregion

    #region Optimizations
    private void OptimizePhysicsSettings()
    {
        // 为高帧率优化物理设置
        Time.fixedDeltaTime = 1.0f / 60.0f; // 60Hz物理更新
        Physics.defaultSolverIterations = 4;
        Physics.defaultSolverVelocityIterations = 1;
        
        Debug.Log("[Simple120FPSManager] ✓ 物理设置已优化");
    }

    private void ApplyMobileOptimizations()
    {
        Debug.Log("[Simple120FPSManager] 应用移动设备优化...");

        // 基本移动设备优化
        QualitySettings.globalTextureMipmapLimit = 1;
        QualitySettings.anisotropicFiltering = AnisotropicFiltering.Disable;
        QualitySettings.realtimeReflectionProbes = false;
        QualitySettings.particleRaycastBudget = 16;
        QualitySettings.lodBias = 0.7f;

        // 阴影优化
        QualitySettings.shadows = ShadowQuality.HardOnly;
        QualitySettings.shadowResolution = ShadowResolution.Low;
        QualitySettings.shadowDistance = 30f;

        // 异步上传优化
        QualitySettings.asyncUploadTimeSlice = 4;
        QualitySettings.asyncUploadBufferSize = 32;
        QualitySettings.asyncUploadPersistentBuffer = true;

        Debug.Log("[Simple120FPSManager] ✓ 移动设备优化已应用");
    }

    private bool IsMobileDevice()
    {
        return Application.platform == RuntimePlatform.Android || 
               Application.platform == RuntimePlatform.IPhonePlayer;
    }
    #endregion

    #region Public Methods
    /// <summary>
    /// 切换120FPS模式
    /// </summary>
    public void Toggle120FPS()
    {
        m_Enable120FPS = !m_Enable120FPS;
        Initialize120FPS();
    }

    /// <summary>
    /// 设置120FPS模式
    /// </summary>
    public void Set120FPS(bool enable)
    {
        m_Enable120FPS = enable;
        Initialize120FPS();
    }

    /// <summary>
    /// 切换FPS计数器显示
    /// </summary>
    public void ToggleFPSCounter()
    {
        m_ShowFPSCounter = !m_ShowFPSCounter;
    }

    /// <summary>
    /// 手动应用移动设备优化
    /// </summary>
    public void ApplyMobileOptimizationsManually()
    {
        ApplyMobileOptimizations();
    }

    /// <summary>
    /// 获取性能状态报告
    /// </summary>
    public string GetPerformanceReport()
    {
        return $"FPS: {m_CurrentFPS:F1}/{Application.targetFrameRate} | " +
               $"VSync: {(QualitySettings.vSyncCount > 0 ? "开" : "关")} | " +
               $"质量: {QualitySettings.names[QualitySettings.GetQualityLevel()]} | " +
               $"物理: {1.0f/Time.fixedDeltaTime:F0}Hz";
    }
    #endregion

    #region Context Menu (Editor Only)
    #if UNITY_EDITOR
    [ContextMenu("启用120FPS")]
    private void Enable120FPS()
    {
        Set120FPS(true);
    }

    [ContextMenu("禁用120FPS")]
    private void Disable120FPS()
    {
        Set120FPS(false);
    }

    [ContextMenu("切换FPS显示")]
    private void ToggleFPSDisplay()
    {
        ToggleFPSCounter();
    }

    [ContextMenu("应用移动优化")]
    private void ApplyMobileOpt()
    {
        ApplyMobileOptimizationsManually();
    }

    [ContextMenu("显示性能报告")]
    private void ShowPerformanceReport()
    {
        Debug.Log($"[性能报告] {GetPerformanceReport()}");
    }
    #endif
    #endregion
}
