using UnityEngine;
using UnityEngine.UI;
using T<PERSON><PERSON>;
using Performance;

namespace UI
{
    /// <summary>
    /// 性能监控UI - 显示FPS、CPU/GPU使用率等性能信息
    /// </summary>
    public class PerformanceMonitorUI : MonoBehaviour
    {
        #region Serialized Fields
        [Header("UI组件")]
        [SerializeField, <PERSON>lt<PERSON>("FPS显示文本")]
        private TextMeshProUGUI m_FPSText;

        [SerializeField, Tooltip("性能统计文本")]
        private TextMeshProUGUI m_PerformanceStatsText;

        [SerializeField, Tooltip("移动设备优化信息文本")]
        private TextMeshProUGUI m_MobileOptimizationText;

        [SerializeField, Tooltip("性能监控面板")]
        private GameObject m_PerformancePanel;

        [Header("显示设置")]
        [SerializeField, Tooltip("显示FPS")]
        private bool m_ShowFPS = true;

        [SerializeField, Toolt<PERSON>("显示详细统计")]
        private bool m_ShowDetailedStats = false;

        [Serialize<PERSON><PERSON>, <PERSON><PERSON><PERSON>("显示移动优化信息")]
        private bool m_ShowMobileOptimization = true;

        [SerializeField, Tooltip("更新间隔（秒）")]
        [Range(0.1f, 2.0f)]
        private float m_UpdateInterval = 0.5f;

        [Header("颜色设置")]
        [SerializeField, Tooltip("良好FPS颜色")]
        private Color m_GoodFPSColor = Color.green;

        [SerializeField, Tooltip("中等FPS颜色")]
        private Color m_MediumFPSColor = Color.yellow;

        [SerializeField, Tooltip("差FPS颜色")]
        private Color m_BadFPSColor = Color.red;
        #endregion

        #region Private Fields
        private PerformanceManager m_PerformanceManager;
        private MobilePerformanceOptimizer m_MobileOptimizer;
        private float m_LastUpdateTime;
        private bool m_IsVisible = true;

        // FPS计算
        private int m_FrameCount;
        private float m_FPSTimer;
        private float m_CurrentFPS;
        #endregion

        #region Unity Lifecycle
        private void Start()
        {
            InitializeComponents();
            SetupUI();
        }

        private void Update()
        {
            UpdateFPSCounter();
            
            if (Time.time - m_LastUpdateTime >= m_UpdateInterval)
            {
                UpdateUI();
                m_LastUpdateTime = Time.time;
            }

            // 快捷键切换显示
            if (Input.GetKeyDown(KeyCode.F1))
            {
                ToggleVisibility();
            }
        }
        #endregion

        #region Initialization
        private void InitializeComponents()
        {
            // 查找性能管理器
            m_PerformanceManager = FindObjectOfType<PerformanceManager>();
            if (m_PerformanceManager == null)
            {
                Debug.LogWarning("[PerformanceMonitorUI] 未找到PerformanceManager，某些功能可能不可用");
            }

            // 查找移动设备优化器
            m_MobileOptimizer = FindObjectOfType<MobilePerformanceOptimizer>();
            if (m_MobileOptimizer == null && IsMobileDevice())
            {
                Debug.LogWarning("[PerformanceMonitorUI] 未找到MobilePerformanceOptimizer，移动设备优化信息不可用");
            }
        }

        private void SetupUI()
        {
            // 设置初始可见性
            if (m_PerformancePanel != null)
            {
                m_PerformancePanel.SetActive(m_IsVisible);
            }

            // 初始化文本组件
            if (m_FPSText != null)
            {
                m_FPSText.text = "FPS: --";
            }

            if (m_PerformanceStatsText != null)
            {
                m_PerformanceStatsText.text = "性能统计加载中...";
                m_PerformanceStatsText.gameObject.SetActive(m_ShowDetailedStats);
            }

            if (m_MobileOptimizationText != null)
            {
                m_MobileOptimizationText.gameObject.SetActive(m_ShowMobileOptimization && IsMobileDevice());
            }
        }

        private bool IsMobileDevice()
        {
            return Application.platform == RuntimePlatform.Android || 
                   Application.platform == RuntimePlatform.IPhonePlayer;
        }
        #endregion

        #region FPS Calculation
        private void UpdateFPSCounter()
        {
            m_FrameCount++;
            m_FPSTimer += Time.unscaledDeltaTime;

            if (m_FPSTimer >= 1.0f)
            {
                m_CurrentFPS = m_FrameCount / m_FPSTimer;
                m_FrameCount = 0;
                m_FPSTimer = 0.0f;
            }
        }
        #endregion

        #region UI Updates
        private void UpdateUI()
        {
            if (!m_IsVisible) return;

            UpdateFPSDisplay();
            
            if (m_ShowDetailedStats)
            {
                UpdatePerformanceStats();
            }

            if (m_ShowMobileOptimization && IsMobileDevice())
            {
                UpdateMobileOptimizationInfo();
            }
        }

        private void UpdateFPSDisplay()
        {
            if (m_FPSText == null || !m_ShowFPS) return;

            // 使用PerformanceManager的FPS如果可用，否则使用本地计算的FPS
            float displayFPS = m_PerformanceManager != null ? m_PerformanceManager.CurrentFPS : m_CurrentFPS;

            // 设置FPS文本和颜色
            m_FPSText.text = $"FPS: {displayFPS:F0}";
            m_FPSText.color = GetFPSColor(displayFPS);

            // 添加目标FPS信息
            if (m_PerformanceManager != null && m_PerformanceManager.Is120FPSEnabled)
            {
                m_FPSText.text += $" / 120";
            }
            else
            {
                m_FPSText.text += $" / {Application.targetFrameRate}";
            }
        }

        private void UpdatePerformanceStats()
        {
            if (m_PerformanceStatsText == null) return;

            string statsText = "";

            // 基本性能信息
            statsText += $"目标帧率: {Application.targetFrameRate}\n";
            statsText += $"垂直同步: {(QualitySettings.vSyncCount > 0 ? "开启" : "关闭")}\n";
            statsText += $"质量等级: {QualitySettings.names[QualitySettings.GetQualityLevel()]}\n";

            // PerformanceManager信息
            if (m_PerformanceManager != null)
            {
                statsText += $"渲染缩放: {m_PerformanceManager.RenderScale:F2}\n";
                statsText += $"120FPS模式: {(m_PerformanceManager.Is120FPSEnabled ? "启用" : "禁用")}\n";
            }

            // 系统信息
            statsText += $"设备型号: {SystemInfo.deviceModel}\n";
            statsText += $"GPU: {SystemInfo.graphicsDeviceName}\n";
            statsText += $"内存: {SystemInfo.systemMemorySize} MB\n";
            statsText += $"显存: {SystemInfo.graphicsMemorySize} MB";

            m_PerformanceStatsText.text = statsText;
        }

        private void UpdateMobileOptimizationInfo()
        {
            if (m_MobileOptimizationText == null || m_MobileOptimizer == null) return;

            string optimizationText = "";

            // 移动设备优化信息
            optimizationText += $"优化状态: {(m_MobileOptimizer.IsOptimizationActive ? "激活" : "正常")}\n";
            optimizationText += $"优化等级: {m_MobileOptimizer.OptimizationLevel}/3\n";
            optimizationText += $"CPU使用率: {m_MobileOptimizer.CurrentCPUUsage:P0}\n";
            optimizationText += $"GPU使用率: {m_MobileOptimizer.CurrentGPUUsage:P0}\n";
            optimizationText += $"设备温度: {m_MobileOptimizer.DeviceTemperature:F1}°C\n";

            // 电池信息
            if (SystemInfo.batteryLevel > 0)
            {
                optimizationText += $"电池电量: {SystemInfo.batteryLevel:P0}\n";
                optimizationText += $"电池状态: {SystemInfo.batteryStatus}";
            }

            m_MobileOptimizationText.text = optimizationText;
        }

        private Color GetFPSColor(float fps)
        {
            int targetFPS = Application.targetFrameRate;
            
            if (fps >= targetFPS * 0.9f)
                return m_GoodFPSColor;
            else if (fps >= targetFPS * 0.7f)
                return m_MediumFPSColor;
            else
                return m_BadFPSColor;
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// 切换性能监控UI的可见性
        /// </summary>
        public void ToggleVisibility()
        {
            m_IsVisible = !m_IsVisible;
            
            if (m_PerformancePanel != null)
            {
                m_PerformancePanel.SetActive(m_IsVisible);
            }

            Debug.Log($"[PerformanceMonitorUI] 性能监控UI {(m_IsVisible ? "显示" : "隐藏")}");
        }

        /// <summary>
        /// 设置FPS显示开关
        /// </summary>
        public void SetShowFPS(bool show)
        {
            m_ShowFPS = show;
            
            if (m_FPSText != null)
            {
                m_FPSText.gameObject.SetActive(show);
            }
        }

        /// <summary>
        /// 设置详细统计显示开关
        /// </summary>
        public void SetShowDetailedStats(bool show)
        {
            m_ShowDetailedStats = show;
            
            if (m_PerformanceStatsText != null)
            {
                m_PerformanceStatsText.gameObject.SetActive(show);
            }
        }

        /// <summary>
        /// 设置移动优化信息显示开关
        /// </summary>
        public void SetShowMobileOptimization(bool show)
        {
            m_ShowMobileOptimization = show;
            
            if (m_MobileOptimizationText != null && IsMobileDevice())
            {
                m_MobileOptimizationText.gameObject.SetActive(show);
            }
        }

        /// <summary>
        /// 设置更新间隔
        /// </summary>
        public void SetUpdateInterval(float interval)
        {
            m_UpdateInterval = Mathf.Clamp(interval, 0.1f, 2.0f);
        }

        /// <summary>
        /// 重置性能统计
        /// </summary>
        public void ResetPerformanceStats()
        {
            if (m_PerformanceManager != null)
            {
                m_PerformanceManager.ResetPerformanceStats();
            }

            Debug.Log("[PerformanceMonitorUI] 性能统计已重置");
        }
        #endregion

        #region Button Callbacks
        /// <summary>
        /// 切换120FPS模式按钮回调
        /// </summary>
        public void OnToggle120FPS()
        {
            if (m_PerformanceManager != null)
            {
                bool newState = !m_PerformanceManager.Is120FPSEnabled;
                m_PerformanceManager.Toggle120FPS(newState);
                
                Debug.Log($"[PerformanceMonitorUI] 120FPS模式已{(newState ? "启用" : "禁用")}");
            }
        }

        /// <summary>
        /// 手动优化性能按钮回调
        /// </summary>
        public void OnOptimizePerformance()
        {
            if (m_PerformanceManager != null)
            {
                m_PerformanceManager.OptimizePerformance();
            }

            Debug.Log("[PerformanceMonitorUI] 手动触发性能优化");
        }

        /// <summary>
        /// 设置移动优化等级按钮回调
        /// </summary>
        public void OnSetMobileOptimizationLevel(int level)
        {
            if (m_MobileOptimizer != null)
            {
                m_MobileOptimizer.SetOptimizationLevel(level);
                Debug.Log($"[PerformanceMonitorUI] 移动优化等级设置为 {level}");
            }
        }
        #endregion
    }
}
